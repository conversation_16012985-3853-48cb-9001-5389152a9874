<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
            color: #155724;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端API测试</h1>
        <p>这个页面模拟前端的API调用方式，测试订单统计API是否正常工作。</p>
        
        <div>
            <button onclick="testOrderStatsAPI()">测试订单统计API</button>
            <button onclick="testDashboardStatsAPI()">测试首页统计API</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        // 模拟前端的axios配置
        const api = {
            baseURL: 'http://localhost:8082/api',
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        // 模拟前端的请求方法
        async function request(config) {
            const url = api.baseURL + config.url;
            console.log('发送请求:', { url, method: config.method });
            
            try {
                const response = await fetch(url, {
                    method: config.method || 'GET',
                    headers: api.headers,
                    body: config.data ? JSON.stringify(config.data) : undefined
                });
                
                const data = await response.json();
                console.log('收到响应:', data);
                
                // 模拟前端响应拦截器的行为 - 直接返回data
                return data;
            } catch (error) {
                console.error('请求错误:', error);
                throw error;
            }
        }

        // 模拟前端的ordersAPI.getAdminOrderStatistics方法
        async function getAdminOrderStatistics() {
            return request({
                url: '/orders/admin/statistics',
                method: 'GET'
            });
        }

        // 模拟前端的ordersAPI.getAdminDashboardStatistics方法
        async function getAdminDashboardStatistics() {
            return request({
                url: '/admin/dashboard/statistics',
                method: 'GET'
            });
        }

        // 模拟前端的loadStatistics函数
        async function testOrderStatsAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="result">正在测试订单统计API...</div>';
            
            try {
                console.log('开始加载订单统计数据...');
                const response = await getAdminOrderStatistics();
                
                console.log('订单统计API响应:', response);

                // 模拟前端的处理逻辑
                if (response && response.success) {
                    const statistics = response.data;
                    console.log('订单统计数据加载成功:', statistics);
                    
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ 订单统计API测试成功！</h3>
                            <p><strong>订单总数:</strong> ${statistics.total_orders}</p>
                            <p><strong>待支付:</strong> ${statistics.pending_payment}</p>
                            <p><strong>已支付:</strong> ${statistics.paid}</p>
                            <p><strong>已发货:</strong> ${statistics.shipped}</p>
                            <p><strong>已完成:</strong> ${statistics.completed}</p>
                            <p><strong>已取消:</strong> ${statistics.cancelled}</p>
                            <h4>原始响应数据:</h4>
                            <pre>${JSON.stringify(response, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    console.error('获取订单统计失败:', response?.message || '未知错误');
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 订单统计API测试失败</h3>
                            <p>错误信息: ${response?.message || '未知错误'}</p>
                            <pre>${JSON.stringify(response, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('获取订单统计失败:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 订单统计API请求失败</h3>
                        <p>错误信息: ${error.message || '网络错误'}</p>
                    </div>
                `;
            }
        }

        async function testDashboardStatsAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="result">正在测试首页统计API...</div>';
            
            try {
                console.log('开始加载首页统计数据...');
                const response = await getAdminDashboardStatistics();
                
                console.log('首页统计API响应:', response);

                if (response && response.success) {
                    const statistics = response.data;
                    console.log('首页统计数据加载成功:', statistics);
                    
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ 首页统计API测试成功！</h3>
                            <p><strong>商品总数:</strong> ${statistics.totalProducts}</p>
                            <p><strong>分类总数:</strong> ${statistics.totalCategories}</p>
                            <p><strong>订单总数:</strong> ${statistics.totalOrders}</p>
                            <p><strong>用户总数:</strong> ${statistics.totalUsers}</p>
                            <h4>原始响应数据:</h4>
                            <pre>${JSON.stringify(response, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    console.error('获取首页统计失败:', response?.message || '未知错误');
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 首页统计API测试失败</h3>
                            <p>错误信息: ${response?.message || '未知错误'}</p>
                            <pre>${JSON.stringify(response, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('获取首页统计失败:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 首页统计API请求失败</h3>
                        <p>错误信息: ${error.message || '网络错误'}</p>
                    </div>
                `;
            }
        }

        // 页面加载时自动测试订单统计API
        window.onload = function() {
            testOrderStatsAPI();
        };
    </script>
</body>
</html>
