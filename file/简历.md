2025/9/10 14:34 18936805013 Print Resume 教育经历 蒋荣伟 本科 2026.6毕业 <EMAIL> 软件工程 软件开发岗 常州工学院 软件工程·本科 2022/9/1 2026/6/1 主修课程：分布式应用开发，大数据与云计算，移动应用技术开发，数据库系统概论，web前 端应用，计算机网络，计算机系统概论等。 项目经历 清风商城 前端技术栈 Vue 3.2.13 + Composition API+Axios+Webpack+ESLint 后端技术栈 Spring Boot+ MyBatis Plus 3+MySQL+ JWT Token+Spring Boot Mail + QQ邮箱 SMTP+支付宝Easy SDK 2.2.0（沙箱环境）等 项目描述 这是一个前后端分离的现代化电商管理系统，包含用户商城和管理员后台两套完整的业务 系统。项目采用Vue3+Element Plus构建响应式前端界面，Spring Boot+MyBatis Plus构 建RESTful API后端服务，MySQL作为数据存储，实现了从商品展示、购物下单到后台管 理的完整电商业务流程 项目难点 1.并发库存控制 问题：多用户同时购买同一商品时的库存超卖问题 解决方案：使用 @Transactional 事务注解 + 数据库行级锁，在订单创建前进行库存校 验，确保数据一致性 技术要点：事务隔离级别控制、乐观锁机制、库存预扣设计 2.支付宝集成与回调处理 问题：支付宝沙箱环境配置、异步通知处理、订单状态同步 解决方案：配置支付宝Easy SDK，实现同步/异步回调接口，通过订单号关联支付状态 技术要点：RSA2签名验证、支付状态轮询、回调幂等性处理 3.前后端权限控制 问题：用户角色区分（普通用户/管理员）、页面访问权限、API接口权限 https://magicv.art/app/workbench/613d01e7-23da-403d-b2e2-67896a250bc5 1/2 2025/9/10 14:34 Print Resume 解决方案：前端路由守卫 + 后端注解式权限控制，通过用户状态字段区分角色权限 技术要点：Vue Router导航守卫、JWT Token验证、角色基础访问控制 4.文件上传与静态资源管理 问题：商品图片上传、图片存储路径、前端图片访问跨域 解决方案：Spring Boot文件上传 + 静态资源映射配置，解决跨域访问问题 技术要点：MultipartFile处理、静态资源路径映射、CORS跨域配置 5.邮件验证码系统 问题：验证码生成、邮件发送、验证码过期控制、防刷机制 解决方案：集成Spring Boot Mail，设计验证码表结构，实现验证码生命周期管理 技术要点：SMTP邮件发送、验证码时效性控制、发送频率限制 专业技能 熟悉java 核 心 技 术 ,对 面 向 对 象 思 想 有 一 定 的 理 解 ,对于java语言中的异常机制、常用 集合、IO、多线程、反射等技术做过一定学习。 熟悉java 常 用 开 源 框 架 :Spring 、 MyBatis 、 SpringBoot 、 SpringCloud 、 MyBatisPlus 。 对于Spring的IoC和AOP、MVC思想、ORM思想、SpringBoot的自动配置原理、微服务架构有 一定的理解。 对于HTML、CSS、JavaScript做过系统的学习。熟悉jQuery、Vue3、Axios、ElementPlus。 基于这些技术做过项目 可 以 熟 练 使 用 MySQL 数 据 库 。能够基于数据库范式根据需求分析设计数据表。有一定的 SQL编写能力,可以熟练编写常见的增删改操作、单表和多表连接统计SQL语句。 熟悉常见的软件设计模式，例如：工厂模式，抽象工厂模式、单例模式，策略模式等。 熟练使用VMware搭建Linux虚拟机，并使用 Redis 实现缓存、集群搭建等操作 熟练使用 IntelliJ IDEA、Eclipse、Navicat、VS Code、Hbuilder X 等开发工具 掌握c语言，python,鸿蒙arkts等编程语言的基础语法 荣誉证书 英语四级 软考软件设计师中级资格证书 PCTA初级证书 https://magicv.art/app/workbench/613d01e7-23da-403d-b2e2-67896a250bc5 2/2