# "嵌入式人才培养"企业实习双周报告

| 实习日期             | 2025年9月1日 - 2025年9月13日                                |
| -------------------- | ------------------------------------------------------------ |
| 实习工作情况记录     | 在这两周的实习期间，我参与了员工管理系统（HRM）的开发工作，并承担了主要的开发任务。在团队合作的过程中，我积累了丰富的技术经验，并在系统设计和开发方面有了更深入的理解。<br><br>**第一周（9月1日-9月6日）：系统架构搭建与基础功能开发**<br>首先，我和团队成员一起进行需求分析，明确了员工管理系统的核心功能，包括员工管理、部门管理、用户认证、数据统计等。基于这些需求，我们制定了系统的总体架构，采用前后端分离的设计模式：前端使用Vue 3 + Element Plus + Vite构建现代化的用户界面，后端使用Spring Boot 2.7.6 + MyBatis-Plus 3.5.3.1进行业务逻辑处理和数据持久化。<br><br>在系统设计过程中，我们充分考虑了系统的可扩展性和高效性。数据库设计包含了员工表（employee）、部门表（department）、管理员表（admin）等核心表结构，并建立了合理的关联关系。同时配置了Redis缓存和JWT Token认证机制，确保系统的性能和安全性。<br><br>**第二周（9月7日-9月13日）：核心功能实现与问题解决**<br>在开发阶段，我主要负责了员工管理模块和用户认证模块的开发任务。我使用Spring Boot框架实现了员工的增删改查功能，通过MyBatis-Plus与MySQL数据库进行交互，确保数据的准确性和一致性。实现了员工信息的分页查询、条件搜索、批量删除等高级功能。<br><br>在前端开发中，我使用Vue 3的Composition API构建了响应式的用户界面，集成Element Plus组件库实现了美观且功能完整的表单、表格、对话框等UI组件。实现了员工列表展示、添加员工、编辑员工、删除员工等核心功能，并添加了表单验证和用户友好的交互反馈。<br><br>**技术难点攻克**<br>在开发过程中，我遇到并成功解决了多个技术难点：<br>1. **跨域问题**：配置了CORS跨域支持，确保前后端正常通信<br>2. **日期格式处理**：解决了前后端日期格式不一致的问题，统一使用ISO格式<br>3. **表单验证**：实现了前端表单验证和后端数据校验的双重保障<br>4. **路由配置**：配置了Vue Router实现单页面应用的路由管理<br>5. **邮箱注册功能**：修复了用户注册时邮箱信息无法保存到数据库的问题，更新了AdminController和AdminService以支持邮箱参数处理<br><br>**系统功能完善**<br>系统最终实现了以下核心功能：<br>- 用户认证：登录、注册、退出登录、会话管理<br>- 员工管理：员工信息的增删改查、分页显示、条件搜索<br>- 部门管理：部门信息维护、员工部门关联<br>- 数据统计：员工统计图表、部门分布可视化<br>- 系统管理：侧边栏菜单管理、用户权限控制<br><br>通过这次实习，我不仅在员工管理系统的开发过程中获得了丰富的实践经验，还在Spring Boot、Vue 3、MyBatis-Plus以及前后端分离架构的应用上取得了显著进步。 |
| 工作、学习体会及收获 | 在参与员工管理系统的开发过程中，我获得了丰富的技术经验和实际操作技能，这次项目让我在多个方面都得到了显著提升。<br><br>**技术能力提升**<br>首先，这次项目让我在技术层面得到了全面提升。我不仅熟练掌握了Spring Boot的应用，还深入研究了现代前端框架Vue 3的使用。在后端开发中，我学会了如何设计RESTful API，使用MyBatis-Plus进行高效的数据库操作，配置Redis缓存提升系统性能。在前端开发中，我掌握了Vue 3的Composition API、响应式数据绑定、组件化开发等核心概念，并能够熟练使用Element Plus构建美观的用户界面。<br><br>**系统架构理解**<br>通过负责员工管理模块的开发，我进一步加强了对前后端分离架构的理解。我学会了如何设计合理的数据库表结构，如何处理表与表之间的关联关系，如何优化SQL查询性能。在处理员工信息的增删改查操作时，我的编码能力和问题解决能力得到了极大的锻炼。<br><br>**问题解决能力**<br>在开发过程中遇到的各种技术难题让我的问题解决能力得到了显著提升。从跨域配置到日期格式处理，从表单验证到路由管理，每一个问题的解决都让我对相关技术有了更深入的理解。特别是在解决邮箱注册功能的问题时，我学会了如何系统性地分析问题、定位根因、制定解决方案并验证效果。<br><br>**开发流程规范**<br>这次项目让我深刻认识到规范开发流程的重要性。从需求分析到系统设计，从编码实现到测试验证，每一个环节都需要严格把控。我学会了如何编写清晰的代码注释，如何进行有效的错误处理，如何设计用户友好的交互界面。<br><br>**团队协作经验**<br>最重要的是，这次项目让我深刻体会到团队合作的价值。在与团队成员的紧密合作中，我们成功应对了开发中的各种挑战，确保了项目的顺利推进。我学会了如何与他人协作开发，如何进行代码版本管理，如何进行技术方案讨论和决策。<br><br>**未来发展方向**<br>通过这次实习，我明确了自己未来的技术发展方向。我希望能够继续深入学习Spring生态系统和Vue生态系统，掌握更多的企业级开发技术，如微服务架构、容器化部署、自动化测试等。同时，我也认识到持续学习和实践的重要性，技术更新迭代很快，只有保持学习的热情才能在技术道路上走得更远。 |
