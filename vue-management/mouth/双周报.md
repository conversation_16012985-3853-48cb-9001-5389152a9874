# “嵌入式人才培养”企业实习月报告

| 实习日期             | 2025年7月14日 - 2024年7月25日                                |
| -------------------- | ------------------------------------------------------------ |
| 实习工作情况记录     | 在这个月的实习期间，我参与了图书馆管理系统的开发工作，并承担了主要的开发任务。在团队合作的过程中，我积累了丰富的技术经验，并在系统设计和开发方面有了更深入的理解。  首先，我和团队成员一起进行需求分析，明确了图书馆管理系统的核心功能，如书籍管理、用户管理、借阅归还管理等。基于这些需求，我们制定了系统的总体架构，并决定采用Spring Boot作为后端开发框架，结合MyBatis进行数据持久化管理。在系统设计过程中，我们还充分考虑了系统的可扩展性和高效性，以确保系统能够在未来进行功能拓展和性能优化。  在开发阶段，我主要负责了书籍管理模块的开发任务。我使用Spring Boot框架实现了书籍的增删改查功能，通过MyBatis与数据库进行交互，确保数据的准确性和一致性。同时，我还处理了多种类型的HTTP请求，并通过Spring MVC框架实现了前后端的有效交互。在开发过程中，我注重代码的可维护性和模块的稳定性，确保书籍管理模块能够在高并发场景下稳定运行。  在安全性方面，我学习并应用了Spring Security技术，针对图书馆系统的需求，实现了用户认证和权限管理功能。我配置了基于角色的访问控制，确保不同用户角色只能访问相应的资源。在此过程中，我深入理解了Spring Security的工作原理，并成功将其集成到系统中，提升了系统的整体安全性。  此外，我还参与了系统的性能优化和测试工作。通过使用Spring Boot Actuator和其他监控工具，我实时跟踪系统性能，并进行了针对性的优化，以提高系统的响应速度和稳定性。同时，我也负责了系统的单元测试和集成测试，确保各模块能够无缝协作，并在不同环境下保持稳定运行。  通过这次实习，我不仅在图书馆管理系统的开发过程中获得了丰富的实践经验，还在Spring Boot、Spring Security以及MyBatis的应用上取得了显著进步。这次实习经历让我在系统设计、开发和性能优化方面积累了宝贵的经验，为我未来的职业发展打下了坚实的基础。 |
| 工作、学习体会及收获 | 在参与图书馆管理系统的开发过程中，我获得了丰富的技术经验和实际操作技能。首先，这次项目让我在技术层面得到了显著提升。我不仅熟练掌握了Spring Boot的应用，还深入研究了系统架构设计和数据模型的优化。在此过程中，我理解了如何设计和实现一个高效、稳定的系统架构，并能够将理论知识应用于实际开发中，处理复杂的业务需求。  其次，通过负责图书管理模块的开发，我进一步加强了对数据库设计、数据操作以及前后端交互的理解。我学会了如何高效管理图书的增删改查操作，并确保数据的一致性和准确性。在处理用户查询和借阅管理时，我的编码能力和问题解决能力得到了极大的锻炼。  此外，在项目后期阶段，我参与了系统的集成测试和性能优化工作。这让我深刻认识到系统稳定性和安全性的关键性，并掌握了通过监控工具和安全配置来提升系统性能和用户体验的方法。  最重要的是，这次项目让我深刻体会到团队合作的价值。在与团队成员的紧密合作中，我们成功应对了开发中的各种挑战，确保了项目的顺利推进。这种合作精神不仅提高了项目的开发效率，也让我在沟通协调和项目管理方面得到了重要的经验积累。 |