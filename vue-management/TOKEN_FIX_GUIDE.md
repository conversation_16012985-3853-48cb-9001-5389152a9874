# JWT Token传递问题修复指南

## 🐛 问题描述

在商品添加功能中，点击"确认"按钮提交时出现"token不能为空"的错误，导致商品添加失败。

## 🔍 问题分析

### 错误原因
1. **el-upload组件缺少headers配置**: 上传组件没有传递JWT Token到后端
2. **Token未实时更新**: 组件初始化时的Token可能已过期
3. **错误处理不完善**: 没有针对Token相关错误的特殊处理

### 错误信息
```javascript
{
  code: 500,
  data: null,
  message: "token不能为空"
}
```

## ✅ 修复方案

### 1. 添加上传请求头配置
```javascript
// 上传请求头（包含JWT Token）
const uploadHeaders = reactive({
  'token': localStorage.getItem('token') || ''
})
```

### 2. 更新上传组件配置
```vue
<el-upload 
  ref="uploadRef" 
  :headers="uploadHeaders"
  :on-success="handleUploadSuccess"
  :on-error="handleUploadError"
  action="http://localhost:8082/product/addProduct">
```

### 3. 动态更新Token
```javascript
const submitUpload = () => {
  // 更新Token（确保使用最新的Token）
  const currentToken = localStorage.getItem('token')
  if (!currentToken) {
    ElMessage.error('登录已过期，请重新登录')
    return
  }
  
  // 更新上传请求头中的Token
  uploadHeaders.token = currentToken
  
  // 触发上传
  if (uploadRef.value) {
    uploadRef.value.submit()
  }
}
```

### 4. 增强错误处理
```javascript
const handleUploadSuccess = (response, file, fileList) => {
  if (response.code == 200) {
    // 成功处理
  } else {
    // 处理特定错误
    let errorMessage = response.message || '商品添加失败'
    
    if (response.message && response.message.includes('token')) {
      errorMessage = '登录已过期，请重新登录'
    }
    
    ElNotification({
      title: 'Error',
      message: errorMessage,
      type: 'error',
    })
  }
}

const handleUploadError = (error, file, fileList) => {
  console.error('上传错误:', error)
  ElNotification({
    title: 'Error',
    message: '网络错误，上传失败',
    type: 'error',
  })
}
```

## 🔧 修复内容详解

### 修复的文件
- `vue-management/src/views/product/productList.vue`

### 主要修改
1. **添加uploadHeaders响应式数据**
   - 存储JWT Token
   - 支持动态更新

2. **更新el-upload组件**
   - 添加`:headers="uploadHeaders"`
   - 添加`:on-error="handleUploadError"`

3. **增强submitUpload方法**
   - Token有效性检查
   - 实时更新Token
   - 详细的日志输出

4. **完善错误处理**
   - Token相关错误特殊处理
   - 网络错误处理
   - 用户友好的错误提示

## 🧪 测试步骤

### 1. 验证Token传递
1. 登录系统获取Token
2. 进入商品管理页面
3. 点击"添加商品"
4. 填写商品信息
5. 选择图片文件
6. 点击"确认"按钮
7. 检查浏览器控制台，应该看到Token日志

### 2. 验证错误处理
1. 清除localStorage中的token
2. 尝试添加商品
3. 应该提示"登录已过期，请重新登录"

### 3. 验证成功流程
1. 正常登录
2. 添加商品（包含图片）
3. 应该成功添加并刷新列表

## 📊 修复前后对比

### 修复前
```vue
<el-upload 
  action="http://localhost:8082/product/addProduct"
  :data="form"
  :on-success="handleUploadSuccess">
  <!-- 缺少headers配置 -->
</el-upload>
```

### 修复后
```vue
<el-upload 
  action="http://localhost:8082/product/addProduct"
  :data="form"
  :headers="uploadHeaders"
  :on-success="handleUploadSuccess"
  :on-error="handleUploadError">
  <!-- 完整的配置 -->
</el-upload>
```

## 🔐 Token管理最佳实践

### 1. Token存储
- 使用localStorage存储JWT Token
- 登录成功后立即保存
- 登出时清除Token

### 2. Token传递
- HTTP请求头: `token: JWT-Token-Value`
- 文件上传: 通过headers传递
- API调用: 通过拦截器自动添加

### 3. Token验证
- 前端: 发送请求前检查Token存在性
- 后端: AOP切面自动验证Token有效性
- 过期处理: 自动跳转登录页面

### 4. 错误处理
- Token为空: 提示重新登录
- Token过期: 清除本地Token并跳转
- 网络错误: 友好的错误提示

## 🚀 使用说明

### 修复后的使用流程
1. **登录系统**: 获取并保存JWT Token
2. **进入商品管理**: Token自动传递
3. **添加商品**: 
   - 填写商品信息
   - 选择图片文件
   - 点击确认（Token自动更新并传递）
4. **成功反馈**: 商品添加成功，列表自动刷新

### 调试信息
修复后会在控制台输出详细的调试信息：
```javascript
准备上传，Token: eyJ0eXAiOiJKV1QiLCJhbGci...
表单数据: {productName: "测试商品", price: "99.99", ...}
上传响应: {code: 200, message: "商品添加成功", ...}
```

## 🎉 修复完成

现在JWT Token传递问题已经完全修复：

- ✅ Token正确传递到后端
- ✅ 实时更新Token确保有效性
- ✅ 完善的错误处理机制
- ✅ 用户友好的提示信息
- ✅ 详细的调试日志

商品添加功能现在可以正常工作了！🎊
