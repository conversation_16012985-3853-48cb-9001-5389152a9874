/**
 * 商品管理API测试脚本
 * 在浏览器控制台中运行此脚本来测试所有商品管理功能
 */

// 测试配置
const API_BASE = 'http://localhost:8082';
let authToken = '';

// 工具函数：发送HTTP请求
async function apiRequest(url, method = 'GET', data = null) {
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'token': authToken
    }
  };
  
  if (data && method !== 'GET') {
    if (method === 'POST' && typeof data === 'object') {
      // 对于POST请求，使用FormData格式
      const formData = new URLSearchParams();
      Object.keys(data).forEach(key => {
        formData.append(key, data[key]);
      });
      options.body = formData;
      options.headers['Content-Type'] = 'application/x-www-form-urlencoded';
    } else {
      options.body = JSON.stringify(data);
    }
  } else if (data && method === 'GET') {
    const params = new URLSearchParams(data);
    url += '?' + params.toString();
  }
  
  const response = await fetch(API_BASE + url, options);
  return await response.json();
}

// 1. 登录获取Token
async function login() {
  console.log('🔐 正在登录...');
  try {
    const response = await apiRequest('/login?username=admin&password=admin');
    if (response.code === 200) {
      authToken = response.data.token;
      console.log('✅ 登录成功，Token:', authToken.substring(0, 20) + '...');
      return true;
    } else {
      console.error('❌ 登录失败:', response.message);
      return false;
    }
  } catch (error) {
    console.error('❌ 登录异常:', error);
    return false;
  }
}

// 2. 获取商品列表
async function testGetProducts() {
  console.log('\n📋 测试获取商品列表...');
  try {
    const response = await apiRequest('/getAllProducts', 'GET', { currentPage: 1 });
    if (response.code === 200) {
      console.log('✅ 获取商品列表成功');
      console.log('📊 商品总数:', response.data.total);
      console.log('📄 当前页商品数:', response.data.records.length);
      if (response.data.records.length > 0) {
        console.log('🛍️ 第一个商品:', response.data.records[0]);
      }
      return response.data.records;
    } else {
      console.error('❌ 获取商品列表失败:', response.message);
      return [];
    }
  } catch (error) {
    console.error('❌ 获取商品列表异常:', error);
    return [];
  }
}

// 3. 添加测试商品
async function testAddProduct() {
  console.log('\n➕ 测试添加商品...');
  const testProduct = {
    productName: '测试商品_' + Date.now(),
    price: 999.99,
    storageNum: 100,
    description: '这是一个API测试商品',
    productImages: null
  };
  
  try {
    const response = await apiRequest('/addProduct', 'POST', testProduct);
    if (response.code === 200) {
      console.log('✅ 添加商品成功');
      console.log('🆕 新商品信息:', testProduct);
      return true;
    } else {
      console.error('❌ 添加商品失败:', response.message);
      return false;
    }
  } catch (error) {
    console.error('❌ 添加商品异常:', error);
    return false;
  }
}

// 4. 搜索商品
async function testSearchProduct() {
  console.log('\n🔍 测试搜索商品...');
  try {
    const response = await apiRequest('/searchProductByName', 'GET', { productName: 'iPhone' });
    if (response.code === 200) {
      console.log('✅ 搜索商品成功');
      console.log('🔍 搜索结果数量:', response.data.length);
      if (response.data.length > 0) {
        console.log('📱 搜索到的商品:', response.data[0]);
      }
      return response.data;
    } else {
      console.error('❌ 搜索商品失败:', response.message);
      return [];
    }
  } catch (error) {
    console.error('❌ 搜索商品异常:', error);
    return [];
  }
}

// 5. 测试库存预警
async function testLowStockAlert() {
  console.log('\n⚠️ 测试库存预警...');
  try {
    const response = await apiRequest('/getLowStockProducts', 'GET', { threshold: 50 });
    if (response.code === 200) {
      console.log('✅ 库存预警查询成功');
      console.log('📉 库存不足商品数量:', response.data.length);
      if (response.data.length > 0) {
        console.log('⚠️ 库存不足的商品:', response.data);
      } else {
        console.log('🎉 所有商品库存充足');
      }
      return response.data;
    } else {
      console.error('❌ 库存预警查询失败:', response.message);
      return [];
    }
  } catch (error) {
    console.error('❌ 库存预警查询异常:', error);
    return [];
  }
}

// 6. 测试价格区间查询
async function testPriceRangeSearch() {
  console.log('\n💰 测试价格区间查询...');
  try {
    const response = await apiRequest('/searchProductByPrice', 'GET', { 
      minPrice: 1000, 
      maxPrice: 5000 
    });
    if (response.code === 200) {
      console.log('✅ 价格区间查询成功');
      console.log('💵 价格区间(1000-5000)商品数量:', response.data.length);
      if (response.data.length > 0) {
        console.log('🛍️ 价格区间内的商品:', response.data);
      }
      return response.data;
    } else {
      console.error('❌ 价格区间查询失败:', response.message);
      return [];
    }
  } catch (error) {
    console.error('❌ 价格区间查询异常:', error);
    return [];
  }
}

// 7. 测试更新商品（如果有商品的话）
async function testUpdateProduct(products) {
  if (products.length === 0) {
    console.log('\n⏭️ 跳过更新测试（没有商品）');
    return false;
  }
  
  console.log('\n✏️ 测试更新商品...');
  const productToUpdate = products[0];
  const updateData = {
    productId: productToUpdate.productId,
    productName: productToUpdate.productName + '_已更新',
    price: productToUpdate.price + 100,
    storageNum: productToUpdate.storageNum + 10,
    description: productToUpdate.description + ' [已更新]'
  };
  
  try {
    const response = await apiRequest('/updateProduct', 'POST', updateData);
    if (response.code === 200) {
      console.log('✅ 更新商品成功');
      console.log('📝 更新的商品ID:', productToUpdate.productId);
      return true;
    } else {
      console.error('❌ 更新商品失败:', response.message);
      return false;
    }
  } catch (error) {
    console.error('❌ 更新商品异常:', error);
    return false;
  }
}

// 主测试函数
async function runAllTests() {
  console.log('🚀 开始商品管理API全面测试...');
  console.log('=' * 50);
  
  // 1. 登录
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('❌ 登录失败，终止测试');
    return;
  }
  
  // 2. 获取商品列表
  const products = await testGetProducts();
  
  // 3. 添加商品
  await testAddProduct();
  
  // 4. 搜索商品
  await testSearchProduct();
  
  // 5. 库存预警
  await testLowStockAlert();
  
  // 6. 价格区间查询
  await testPriceRangeSearch();
  
  // 7. 更新商品
  await testUpdateProduct(products);
  
  // 8. 再次获取商品列表验证变化
  console.log('\n🔄 再次获取商品列表验证变化...');
  await testGetProducts();
  
  console.log('\n🎉 所有测试完成！');
  console.log('📝 请检查上述测试结果，确保所有功能正常工作。');
}

// 使用说明
console.log(`
🧪 商品管理API测试脚本已加载！

使用方法：
1. 确保后端服务运行在 http://localhost:8082
2. 在浏览器控制台中运行：runAllTests()
3. 查看测试结果

单独测试某个功能：
- login()                    // 登录
- testGetProducts()          // 获取商品列表
- testAddProduct()           // 添加商品
- testSearchProduct()        // 搜索商品
- testLowStockAlert()        // 库存预警
- testPriceRangeSearch()     // 价格区间查询

开始全面测试请运行：runAllTests()
`);

// 导出函数供外部调用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    login,
    testGetProducts,
    testAddProduct,
    testSearchProduct,
    testLowStockAlert,
    testPriceRangeSearch,
    testUpdateProduct
  };
}
