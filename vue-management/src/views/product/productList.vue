<template>

<div style="margin-bottom: 10px;">
    <el-input
      v-model="productName"
      style="width: 240px"
      placeholder="请输入商品名称"
      :prefix-icon="Search"
    />
    <el-button type="danger" @click="search">搜索</el-button>
</div>

<div>
  <el-button type="warning" @click="addProduct">添加商品</el-button>
  <el-button type="info" @click="handleLowStock">库存预警</el-button>
</div>

  <el-table v-loading="loading" :data="tableData" style="width: 90%">
    <el-table-column label="商品编号" width="120">
      <template #default="scope">
        <div style="display: flex; align-items: center">
          <span style="margin-left: 10px">{{ scope.row.productId }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="商品名称" width="200">
      <template #default="scope">
            <div>{{ scope.row.productName }}</div>
      </template>
    </el-table-column>
    <el-table-column label="价格" width="120">
      <template #default="scope">
            <div>¥{{ scope.row.price.toFixed(2) }}</div>
      </template>
    </el-table-column>

    <el-table-column label="库存" width="100">
      <template #default="scope">
            <div v-if="scope.row.storageNum < 20" style="color: red;">{{ scope.row.storageNum }}</div>
            <div v-else-if="scope.row.storageNum < 50" style="color: orange;">{{ scope.row.storageNum }}</div>
            <div v-else style="color: green;">{{ scope.row.storageNum }}</div>
      </template>
    </el-table-column>

        <el-table-column label="商品描述" width="200">
      <template #default="scope">
            <div>{{ scope.row.description }}</div>
      </template>
    </el-table-column>


  <el-table-column label="商品图片" width="160">
      <template #default="scope">
        <div v-if="scope.row.productImages" class="image-with-download">
          <div class="image-container">
            <el-image
              :src="scope.row.productImages"
              :preview-src-list="[scope.row.productImages]"
              fit="cover"
              style="width: 60px; height: 60px; border-radius: 4px;"
              :preview-teleported="true"
            >
              <template #error>
                <div class="image-error">
                  <span>图片加载失败</span>
                </div>
              </template>
            </el-image>
          </div>
          <div class="download-container">
            <el-button
              size="small"
              type="primary"
              :icon="Download"
              @click.prevent="downloadImage(scope.row)"
              :loading="downloadingIds.includes(scope.row.productId)"
              :disabled="downloadingIds.includes(scope.row.productId)"
              title="下载图片到本地"
            >
              {{ downloadingIds.includes(scope.row.productId) ? '下载中' : '下载' }}
            </el-button>
          </div>
        </div>
        <div v-else class="no-image">
          图片暂无
        </div>
      </template>
    </el-table-column>

    <el-table-column label="操作" >
      <template #default="scope">
        <el-button size="small" @click="handleEdit(scope.$index, scope.row)">
          编辑
        </el-button>
        <el-button
          size="small"
          type="danger"
          @click="handleDelete(scope.$index, scope.row)"
        >
          删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      :page-size="2"
      layout="prev, pager, next"
      :total="totalNum"
      @current-change="handleCurrentChange"
    />

  <el-dialog
    v-model="dialogVisible"
    title="商品添加"
    width="700"
  >
    <el-form :model="form" label-width="auto" style="max-width: 600px;margin:20px;">

     <el-form-item label="商品名称">
        <el-input v-model="form.productName" />
      </el-form-item>

    <el-form-item label="商品价格">
        <el-input v-model="form.price" />
      </el-form-item>

        <el-form-item label="商品库存">
        <el-input v-model="form.storageNum" />
      </el-form-item>

      <el-form-item label="商品描述">
        <el-input v-model="form.description" type="textarea" :rows="3" />
      </el-form-item>

     <el-upload ref="uploadRef" class="upload-demo" drag
  	action="http://localhost:8082/product/addProduct"
  	multiple
  	:data="form"
  	name="file"
  	:headers="uploadHeaders"
  	:on-success="handleUploadSuccess"
  	:on-error="handleUploadError"
     	:auto-upload="false">
  		 <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
  		    <div class="el-upload__text">
  		      拖动文件或<em>点击上传</em>
  		    </div>
  		<template #tip>
  		      <div class="el-upload__tip">
  		        支持jpg、png、gif格式图片
  		      </div>
  		    </template>
     </el-upload>

      <el-form-item>
        <el-button type="primary" @click="submitUpload">确认</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </el-form-item>

    </el-form>
  </el-dialog>

   <el-dialog
    v-model="dialogVisible2"
    title="商品修改"
    width="500"
  >
 <el-form :model="form2" label-width="auto" style="max-width: 600px">

     <el-form-item label="商品名称">
      <el-input v-model="form2.productName" />
    </el-form-item>

     <el-form-item label="价格">
      <el-input v-model="form2.price" />
    </el-form-item>

      <el-form-item label="库存">
      <el-input v-model="form2.storageNum" />
    </el-form-item>

     <el-form-item label="描述">
      <el-input v-model="form2.description" />
    </el-form-item>

     <el-form-item label="图片">
      <el-input v-model="form2.productImages" />
    </el-form-item>

 </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible2 = false">取消</el-button>
        <el-button type="primary" @click="submit2">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog
    v-model="lowStockDialogVisible"
    title="库存预警"
    width="600"
  >
    <el-table :data="lowStockProducts" stripe>
      <el-table-column prop="productName" label="商品名称" />
      <el-table-column prop="storageNum" label="当前库存" width="100">
        <template #default="scope">
          <div style="color: red;">{{ scope.row.storageNum }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="price" label="价格" width="120">
        <template #default="scope">
          ¥{{ scope.row.price.toFixed(2) }}
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>

</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { Search, UploadFilled, Download } from '@element-plus/icons-vue'
import { get, post } from '@/api/api'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const totalNum = ref(0)
const productName = ref('')

// 对话框控制
const dialogVisible = ref(false)
const dialogVisible2 = ref(false)
const lowStockDialogVisible = ref(false)
const lowStockProducts = ref([])

// 上传组件引用
const uploadRef = ref(null)

// 下载状态管理
const downloadingIds = ref([])

// 上传请求头（包含JWT Token）
const uploadHeaders = reactive({
  'token': localStorage.getItem('token') || ''
})

// 表单数据
const form = reactive({
  productName: '',
  price: '',
  storageNum: '',
  description: ''
})

const form2 = reactive({
  productId: null,
  productName: '',
  price: '',
  storageNum: '',
  description: '',
  productImages: ''
})

// 获取商品列表
const getProductList = async () => {
  loading.value = true
  try {
    const response = await get('/getAllProducts', { currentPage: currentPage.value })
    if (response.code === 200) {
      tableData.value = response.data.records || []
      totalNum.value = response.data.total || 0
    } else {
      ElMessage.error(response.message || '获取商品列表失败')
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
    ElMessage.error('获取商品列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索商品
const search = async () => {
  if (!productName.value.trim()) {
    currentPage.value = 1
    await getProductList()
    return
  }

  loading.value = true
  try {
    const response = await get('/searchProductByName', { productName: productName.value })
    if (response.code === 200) {
      tableData.value = response.data || []
      totalNum.value = tableData.value.length
    } else {
      ElMessage.error(response.message || '搜索失败')
    }
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败')
  } finally {
    loading.value = false
  }
}

// 添加商品
const addProduct = () => {
  Object.assign(form, {
    productName: '',
    price: '',
    storageNum: '',
    description: ''
  })

  // 更新Token
  const currentToken = localStorage.getItem('token')
  if (currentToken) {
    uploadHeaders.token = currentToken
  }

  // 清空上传文件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  dialogVisible.value = true
}

// 编辑商品
const handleEdit = (index, row) => {
  Object.assign(form2, row)
  dialogVisible2.value = true
}

// 删除商品
const handleDelete = async (index, row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除商品"${row.productName}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await post('/deleteProduct', { productId: row.productId })
    if (response.code === 200) {
      ElMessage.success('删除成功')
      await getProductList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 提交上传
const submitUpload = () => {
  // 验证表单数据
  if (!form.productName.trim()) {
    ElMessage.error('请输入商品名称')
    return
  }
  if (!form.price || form.price <= 0) {
    ElMessage.error('请输入正确的商品价格')
    return
  }
  if (!form.storageNum || form.storageNum < 0) {
    ElMessage.error('请输入正确的库存数量')
    return
  }

  // 更新Token（确保使用最新的Token）
  const currentToken = localStorage.getItem('token')
  if (!currentToken) {
    ElMessage.error('登录已过期，请重新登录')
    return
  }

  // 更新上传请求头中的Token
  uploadHeaders.token = currentToken

  console.log('准备上传，Token:', currentToken.substring(0, 20) + '...')
  console.log('表单数据:', form)

  if (uploadRef.value) {
    uploadRef.value.submit()
  } else {
    ElMessage.error('请选择要上传的图片')
  }
}

// 上传成功后的处理
const handleUploadSuccess = (response, file, fileList) => {
  console.log('上传响应:', response)
  console.log('上传文件:', file)

  if (response.code == 200) {
    ElNotification({
      title: 'Success',
      message: response.message || '商品添加成功',
      type: 'success',
    })

    // 关闭对话框
    dialogVisible.value = false

    // 清空表单
    Object.assign(form, {
      productName: '',
      price: '',
      storageNum: '',
      description: ''
    })

    // 清空文件选择
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
    }

    // 刷新列表
    getProductList()
  } else {
    // 处理特定错误
    let errorMessage = response.message || '商品添加失败'

    if (response.message && response.message.includes('token')) {
      errorMessage = '登录已过期，请重新登录'
      // 可以考虑跳转到登录页面
      // router.push('/login')
    }

    ElNotification({
      title: 'Error',
      message: errorMessage,
      type: 'error',
    })

    console.error('上传失败:', response)

    // 清空文件选择
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
    }
  }
}

// 上传失败的处理
const handleUploadError = (error, file, fileList) => {
  console.error('上传错误:', error)
  ElNotification({
    title: 'Error',
    message: '网络错误，上传失败',
    type: 'error',
  })

  // 清空文件选择
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 提交修改表单
const submit2 = async () => {
  try {
    const response = await post('/updateProduct', form2)
    if (response.code === 200) {
      ElMessage.success('修改成功')
      dialogVisible2.value = false
      await getProductList()
    } else {
      ElMessage.error(response.message || '修改失败')
    }
  } catch (error) {
    console.error('修改失败:', error)
    ElMessage.error('修改失败')
  }
}

// 库存预警
const handleLowStock = async () => {
  try {
    const response = await get('/getLowStockProducts', { threshold: 50 })
    if (response.code === 200) {
      lowStockProducts.value = response.data || []
      lowStockDialogVisible.value = true
      if (lowStockProducts.value.length === 0) {
        ElMessage.success('当前没有库存不足的商品')
      }
    } else {
      ElMessage.error(response.message || '获取库存预警失败')
    }
  } catch (error) {
    console.error('获取库存预警失败:', error)
    ElMessage.error('获取库存预警失败')
  }
}

// 当前页变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  getProductList()
}

// 下载图片
const downloadImage = async (product) => {
  if (!product.productImages) {
    ElMessage.error('该商品没有图片')
    return
  }

  // 检查是否已在下载中
  if (downloadingIds.value.includes(product.productId)) {
    return
  }

  try {
    console.log('开始下载图片:', product.productImages)

    // 添加到下载中列表
    downloadingIds.value.push(product.productId)

    const imageUrl = product.productImages
    const fileName = getFileNameFromUrl(imageUrl, product.productName)

    // 统一使用blob方式下载，确保不会跳转页面
    await downloadImageAsBlob(imageUrl, fileName)

  } catch (error) {
    console.error('下载图片失败:', error)
    ElMessage.error('下载图片失败: ' + error.message)
  } finally {
    // 从下载中列表移除
    const index = downloadingIds.value.indexOf(product.productId)
    if (index > -1) {
      downloadingIds.value.splice(index, 1)
    }
  }
}



// 从URL中提取文件名
const getFileNameFromUrl = (url, productName) => {
  try {
    // 从URL中提取文件名
    const urlParts = url.split('/')
    let originalFileName = urlParts[urlParts.length - 1]

    // 移除URL参数
    if (originalFileName.includes('?')) {
      originalFileName = originalFileName.split('?')[0]
    }

    // 如果有文件扩展名，使用原始文件名
    if (originalFileName.includes('.')) {
      return originalFileName
    }

    // 否则使用商品名称 + 默认扩展名
    const cleanProductName = productName
      .replace(/[<>:"/\\|?*]/g, '_')  // 替换Windows不允许的文件名字符
      .replace(/\s+/g, '_')           // 替换空格为下划线
      .substring(0, 50)               // 限制长度

    return `${cleanProductName}_商品图片.jpg`
  } catch (error) {
    console.error('生成文件名失败:', error)
    return `商品图片_${Date.now()}.jpg`
  }
}

// 通过blob方式下载图片（标准浏览器下载方式）
const downloadImageAsBlob = async (imageUrl, fileName) => {
  try {
    console.log('尝试blob方式下载:', imageUrl)

    // 使用fetch获取图片数据
    const response = await fetch(imageUrl, {
      method: 'GET',
      mode: 'cors', // 明确指定CORS模式
      headers: {
        'Accept': 'image/*',
      },
      // 根据URL判断是否包含凭据
      credentials: imageUrl.startsWith('http://localhost:8082/') ? 'include' : 'omit'
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    // 检查响应类型
    const contentType = response.headers.get('content-type')
    console.log('响应内容类型:', contentType)

    // 放宽内容类型检查，有些服务器可能不返回正确的content-type
    if (contentType && !contentType.startsWith('image/') && !contentType.includes('octet-stream')) {
      console.warn('警告: 响应可能不是图片格式，但继续尝试下载')
    }

    // 获取图片blob数据
    const blob = await response.blob()
    console.log('获取到blob数据，大小:', blob.size, 'bytes')

    if (blob.size === 0) {
      throw new Error('图片文件为空')
    }

    // 创建下载链接并触发下载（确保不会跳转页面）
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')

    // 设置下载属性
    link.href = downloadUrl
    link.download = fileName
    link.style.display = 'none'

    // 使用事件监听器确保下载行为正确
    link.addEventListener('click', (event) => {
      // 不阻止默认行为，让浏览器处理下载
      console.log('触发下载:', fileName)
    })

    // 添加到DOM，触发下载，然后立即移除
    document.body.appendChild(link)

    // 使用setTimeout确保DOM更新完成后再点击
    setTimeout(() => {
      link.click()
      // 立即移除链接，防止任何可能的页面跳转
      setTimeout(() => {
        if (document.body.contains(link)) {
          document.body.removeChild(link)
        }
      }, 10)
    }, 10)

    // 清理临时URL（延迟清理，确保下载开始）
    setTimeout(() => {
      window.URL.revokeObjectURL(downloadUrl)
    }, 1000) // 增加延迟时间

    ElMessage.success('图片下载成功')
    console.log('图片下载完成:', fileName)

  } catch (error) {
    console.error('blob下载失败:', error)

    // 根据错误类型提供更具体的错误信息
    let errorMessage = '图片下载失败'

    if (error.message.includes('Failed to fetch')) {
      errorMessage = 'CORS跨域访问被阻止，请联系管理员配置服务器'
    } else if (error.message.includes('HTTP 404')) {
      errorMessage = '图片文件不存在'
    } else if (error.message.includes('HTTP 403')) {
      errorMessage = '没有权限访问该图片'
    } else if (error.message.includes('网络')) {
      errorMessage = '网络连接失败，请检查网络'
    } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
      errorMessage = '网络请求失败，可能是CORS问题'
    }

    throw new Error(errorMessage)
  }
}

// 页面加载时获取数据
onMounted(() => {
  getProductList()
})
</script>

<style scoped>
/* 与员工管理页面保持一致的样式 */
.upload-demo {
  width: 100%;
}

.el-upload {
  width: 56%;
}

.el-upload__text {
  font-size: 14px;
  color: #606266;
}

.el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 7px;
}

/* 商品图片相关样式 */
.image-with-download {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  height: 70px;
}

.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.download-container {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  background-color: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  color: #909399;
  font-size: 12px;
  text-align: center;
}

.no-image {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 70px;
  color: #c0c4cc;
  font-size: 12px;
  font-style: italic;
}

/* 图片预览样式优化 */
.el-image {
  cursor: pointer;
  transition: transform 0.2s;
}

.el-image:hover {
  transform: scale(1.05);
}

/* 下载按钮样式 */
.download-container .el-button {
  padding: 4px 8px;
  font-size: 12px;
  height: auto;
  min-height: 24px;
}

.download-container .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}
</style>
