<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>系统仪表板</h1>
      <p>欢迎使用人力资源管理系统！这里是您的数据概览</p>
    </div>
    
    <div class="dashboard-content">
      <!-- 统计卡片 -->
      <div class="stats-cards">
        <el-row :gutter="20">
          <el-col :span="6" v-for="(item, index) in statsData" :key="index">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon" :style="{ backgroundColor: item.color }">
                  <component :is="item.icon" />
                </div>
                <div class="stat-info">
                  <h3>{{ item.value }}</h3>
                  <p>{{ item.label }}</p>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 简单的信息展示 -->
      <div class="info-section">
        <el-card class="info-card">
          <template #header>
            <div class="info-header">
              <h3>系统信息</h3>
              <p>当前系统运行状态良好</p>
            </div>
          </template>
          <div class="info-content">
            <p>数据加载状态: {{ loading ? '加载中...' : '加载完成' }}</p>
            <p>最后更新时间: {{ new Date().toLocaleString() }}</p>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { get } from '../api/api.js'
import { 
  User, 
  UserFilled, 
  OfficeBuilding, 
  ShoppingBag 
} from '@element-plus/icons-vue'

// 加载状态
const loading = ref(true)

// 统计数据
const statsData = ref([
  { label: '员工总数', value: '0', icon: User, color: '#409EFF' },
  { label: '在职员工', value: '0', icon: UserFilled, color: '#67C23A' },
  { label: '部门数量', value: '0', icon: OfficeBuilding, color: '#E6A23C' },
  { label: '商品数量', value: '0', icon: ShoppingBag, color: '#F56C6C' }
])

// 获取统计数据
const fetchStatsData = async () => {
  try {
    loading.value = true
    console.log('开始获取统计数据...')
    
    // 获取员工总数和在职员工数
    const empResponse = await get('/employee/page', { currentPage: 1, pageSize: 1000 });
    console.log('员工数据响应:', empResponse)
    if (empResponse.code === 200) {
      const employees = empResponse.data;
      const totalEmployees = employees.length;
      const activeEmployees = employees.filter(emp => emp.status === 1).length;
      
      statsData.value[0].value = totalEmployees.toString();
      statsData.value[1].value = activeEmployees.toString();
    }
    
    // 获取部门数量
    const deptResponse = await get('/getAllDepts');
    console.log('部门数据响应:', deptResponse)
    if (deptResponse.code === 200) {
      statsData.value[2].value = deptResponse.data.length.toString();
    }
    
    // 获取商品数量
    try {
      const productResponse = await get('/product/page', { currentPage: 1, pageSize: 1000 });
      console.log('商品数据响应:', productResponse)
      if (productResponse.code === 200) {
        statsData.value[3].value = productResponse.data.length.toString();
      }
    } catch (error) {
      console.log('商品数据获取失败，使用默认值:', error)
      statsData.value[3].value = '0';
    }
    
    console.log('统计数据获取完成:', statsData.value)
    
  } catch (error) {
    console.error('获取统计数据失败:', error);
    // 使用默认数据
    statsData.value[0].value = '10';
    statsData.value[1].value = '8';
    statsData.value[2].value = '4';
    statsData.value[3].value = '0';
  } finally {
    loading.value = false
  }
}

// 组件挂载后获取数据
onMounted(async () => {
  console.log('Dashboard组件已挂载')
  await fetchStatsData()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.dashboard-header {
  margin-bottom: 30px;
  text-align: center;
}

.dashboard-header h1 {
  color: #303133;
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: bold;
}

.dashboard-header p {
  color: #909399;
  font-size: 16px;
  margin: 0;
}

.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
}

.stats-cards {
  margin-bottom: 30px;
}

.stat-card {
  height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  color: white;
  font-size: 24px;
}

.stat-info h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.info-section {
  margin-top: 20px;
}

.info-card {
  min-height: 200px;
}

.info-header {
  text-align: center;
}

.info-header h3 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 18px;
}

.info-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.info-content {
  padding: 20px;
  text-align: center;
}

.info-content p {
  margin: 10px 0;
  color: #606266;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }
  
  .dashboard-header h1 {
    font-size: 24px;
  }
  
  .stat-card {
    margin-bottom: 15px;
  }
}
</style>
