<template>
  <div class="common-layout">
    <el-container>
      <!-- 顶部导航栏 -->
      <el-header class="header">
        <div class="header-content">
          <div class="header-left">
            <h2 class="system-title">人力资源管理系统</h2>
          </div>
          <div class="header-right">
            <span class="welcome-text">欢迎，{{ currentUser }}</span>
            <el-button
              type="danger"
              size="small"
              @click="handleLogout"
              :icon="SwitchButton"
            >
              退出登录
            </el-button>
          </div>
        </div>
      </el-header>

      <el-container>
        <el-aside class="aside">
			<Aside />
		</el-aside>
        <el-main class="main">
            <RouterView></RouterView>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { reactive, ref ,onMounted, onUnmounted} from "vue";
import {get,post} from '../api/api.js'
import {RouterView, useRouter } from "vue-router";
import { ElMessageBox, ElMessage } from 'element-plus'
import { SwitchButton } from '@element-plus/icons-vue'
import Aside from '../components/Aside.vue'

const router = useRouter();
let checkLoginTimer = null;

// 当前用户信息
const currentUser = ref(localStorage.getItem("userStatus") || "管理员");

// 检查登录状态的函数
const checkLoginStatus = async () => {
  const username = localStorage.getItem("userStatus");
  if (!username) {
    router.push("/");
    return;
  }

  try {
    const data = await get("/checkLogin", {username: username});
    console.log("登录状态检查结果:", data);

    if (!data.data) {
      console.log("登录已过期，跳转到登录页面");
      // 清除本地存储
      localStorage.removeItem("userStatus");
      // 跳转到登录页面
      router.push("/");
      // 清除定时器
      if (checkLoginTimer) {
        clearInterval(checkLoginTimer);
      }
    }
  } catch (error) {
    console.error("检查登录状态失败:", error);
    router.push("/login");
  }
};

onMounted(async()=>{
  // 立即检查一次登录状态
  await checkLoginStatus();

  // 设置定时检查，每30秒检查一次登录状态
  checkLoginTimer = setInterval(checkLoginStatus, 30000);
});

// 退出登录功能
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '退出确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 清除本地存储
    localStorage.removeItem("userStatus");
    localStorage.removeItem("token");

    // 清除定时器
    if (checkLoginTimer) {
      clearInterval(checkLoginTimer);
    }

    // 显示退出成功消息
    ElMessage.success('退出登录成功');

    // 跳转到登录页面
    router.push("/");

  } catch (error) {
    // 用户取消退出
    console.log('用户取消退出登录');
  }
};

// 组件卸载时清除定时器
onUnmounted(() => {
  if (checkLoginTimer) {
    clearInterval(checkLoginTimer);
  }
});








</script>

<style>
	body{
		margin: 0;
		padding: 0;
         overflow: hidden;
	}

	.header{
		height: 60px;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-bottom: 1px solid #e4e7ed;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	.header-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 100%;
		padding: 0 20px;
	}

	.header-left {
		display: flex;
		align-items: center;
	}

	.system-title {
		color: white;
		margin: 0;
		font-size: 20px;
		font-weight: bold;
		text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
	}

	.header-right {
		display: flex;
		align-items: center;
		gap: 15px;
	}

	.welcome-text {
		color: white;
		font-size: 14px;
		text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
	}

	.aside{
		height: calc(100vh - 60px);
		width: 14%;
		background-color: rgb(84, 92, 100);
	}

	.main{
		height: calc(100vh - 60px);
		width: 86%;
		padding: 0;
		overflow-y: auto;
	}

	/* 退出登录按钮样式优化 */
	.el-button--danger {
		background: rgba(255, 255, 255, 0.2);
		border: 1px solid rgba(255, 255, 255, 0.3);
		color: white;
		transition: all 0.3s ease;
	}

	.el-button--danger:hover {
		background: rgba(255, 255, 255, 0.3);
		border-color: rgba(255, 255, 255, 0.5);
		transform: translateY(-1px);
	}
</style>