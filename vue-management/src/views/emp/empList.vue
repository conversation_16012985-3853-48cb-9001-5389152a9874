<template>

<div style="margin-bottom: 10px;">
    <el-input
      v-model="username"
      style="width: 240px"
      placeholder="请输入用户名"
      :prefix-icon="Search"
    />
    <el-button type="danger" @click="search">搜索</el-button>
</div>

<div>
  <el-button type="warning" @click="addEmp">添加员工</el-button>
</div>

  <el-table v-loading="loading" :data="tableData" style="width: 90%">
    <el-table-column label="员工编号" width="120">
      <template #default="scope">
        <div style="display: flex; align-items: center">
          <span style="margin-left: 10px">{{ scope.row.id }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="员工姓名" width="120">
      <template #default="scope">
            <div>{{ scope.row.name }}</div>
      </template>
    </el-table-column>
    <el-table-column label="年龄" width="120">
      <template #default="scope">
            <div>{{ scope.row.age }}</div>
      </template>
    </el-table-column>

    <el-table-column label="电话" width="130">
      <template #default="scope">
            <div>{{ scope.row.phone }}</div>
      </template>
    </el-table-column>

        <el-table-column label="邮箱" width="120">
      <template #default="scope">
            <div>{{ scope.row.email }}</div>
      </template>
    </el-table-column>


  <el-table-column label="所属部门" width="120">
      <template #default="scope">
            <div>{{ getDepartmentName(scope.row.departmentId) }}</div>
      </template>
    </el-table-column>

    <el-table-column label="职位" width="120">
      <template #default="scope">
            <div>{{ scope.row.position }}</div>
      </template>
    </el-table-column>



    <el-table-column label="状态" width="120">
      <template #default="scope">
            <div v-if="scope.row.status==1">在职</div>
             <div v-else>离职</div>
      </template>
    </el-table-column>


    <el-table-column label="操作" >
      <template #default="scope">
        <el-button size="small" @click="handleEdit(scope.$index, scope.row)">
          编辑
        </el-button>
        <el-button
          size="small"
          type="danger"
          @click="handleDelete(scope.$index, scope.row)"
        >
          删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      :page-size="2"
      layout="prev, pager, next"
      :total="totalNum"
      @current-change="handleCurrentChange"
    />

  <el-dialog
    v-model="dialogVisible"
    title="员工信息修改"
    width="500"
  >
 <el-form :model="form2" label-width="auto" style="max-width: 600px">
     <el-form-item label="员工姓名" required>
      <el-input v-model="form2.name" placeholder="请输入员工姓名" />
    </el-form-item>

    <el-form-item label="性别" required>
      <el-select v-model="form2.gender" placeholder="请选择性别">
        <el-option label="男" value="男" />
        <el-option label="女" value="女" />
      </el-select>
    </el-form-item>

     <el-form-item label="年龄">
      <el-input v-model="form2.age" type="number" placeholder="请输入年龄" />
    </el-form-item>

      <el-form-item label="电话" required>
      <el-input v-model="form2.phone" placeholder="请输入手机号码" />
    </el-form-item>

     <el-form-item label="邮箱" required>
      <el-input v-model="form2.email" placeholder="请输入邮箱地址" />
    </el-form-item>

    <el-form-item label="所属部门" required>
      <el-select
        v-model="form2.departmentId"
        placeholder="请选择部门"
        style="width: 100%"
      >
        <el-option
          v-for="dept in departmentList"
          :key="dept.id"
          :label="dept.name"
          :value="dept.id"
        />
      </el-select>
    </el-form-item>

     <el-form-item label="职位">
      <el-input v-model="form2.position" placeholder="请输入职位" />
    </el-form-item>

    <el-form-item label="入职日期">
      <el-date-picker
        v-model="form2.hireDate"
        type="date"
        placeholder="请选择入职日期"
        style="width: 100%"
      />
    </el-form-item>

<el-form-item label="状态">
      <el-select v-model="form2.status" placeholder="请选择状态">
        <el-option label="在职" :value="1" />
        <el-option label="离职" :value="0" />
      </el-select>
    </el-form-item>

 </el-form>



    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submit">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>



   <el-dialog
    v-model="dialogVisible2"
    title="员工信息添加"
    width="500"
  >
 <el-form :model="form2" label-width="auto" style="max-width: 600px">
     <el-form-item label="员工姓名" required>
      <el-input v-model="form2.name" placeholder="请输入员工姓名" />
    </el-form-item>

    <el-form-item label="性别" required>
      <el-select v-model="form2.gender" placeholder="请选择性别">
        <el-option label="男" value="男" />
        <el-option label="女" value="女" />
      </el-select>
    </el-form-item>

     <el-form-item label="年龄">
      <el-input v-model="form2.age" type="number" placeholder="请输入年龄" />
    </el-form-item>

      <el-form-item label="电话" required>
      <el-input v-model="form2.phone" placeholder="请输入手机号码" />
    </el-form-item>

     <el-form-item label="邮箱" required>
      <el-input v-model="form2.email" placeholder="请输入邮箱地址" />
    </el-form-item>

   <el-form-item label="所属部门" required>
      <el-select v-model="form2.departmentId" placeholder="请选择部门" style="width: 100%">
        <el-option
          v-for="dept in departmentList"
          :key="dept.id"
          :label="dept.name"
          :value="dept.id"
        />
      </el-select>
    </el-form-item>

     <el-form-item label="职位">
      <el-input v-model="form2.position" placeholder="请输入职位" />
    </el-form-item>

    <el-form-item label="入职日期">
      <el-date-picker
        v-model="form2.hireDate"
        type="date"
        placeholder="请选择入职日期"
        style="width: 100%"
      />
    </el-form-item>

<el-form-item label="状态">
      <el-select v-model="form2.status" placeholder="请选择状态">
        <el-option label="在职" :value="1" />
        <el-option label="离职" :value="0" />
      </el-select>
    </el-form-item>

 </el-form>



    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible2 = false">取消</el-button>
        <el-button type="primary" @click="submit2">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>


</template>

<script  setup>
import { ref,reactive,onMounted } from 'vue'
import { get, post } from '../../api/api.js'
import { ElNotification } from 'element-plus'
import {Search } from '@element-plus/icons-vue'

const loading=ref(true);
const dialogVisible=ref(false);

const dialogVisible2=ref(false);


const currentPage=ref(1);//当前页数，默认为第一页

const totalNum=ref(0);//记录总条数(数据库中的总数)

// 部门列表数据
const departmentList = ref([]);

const form2=reactive({
  id: '',
  name: '',
  gender: '',
  age: '',
  phone: '',
  email: '',
  departmentId: null,
  position: '',
  hireDate: '',
  status: 1
});//保存员工信息（添加和编辑共用）
const username=ref("");


// 获取部门列表
const getDepartmentList = async () => {
  try {
    const response = await get('/getAllDepts');
    if (response.code === 200) {
      departmentList.value = response.data;
      console.log('获取部门列表成功:', departmentList.value);
    } else {
      ElNotification({
        title: 'Error',
        message: response.message || '获取部门列表失败',
        type: 'error',
      });
    }
  } catch (error) {
    console.error('获取部门列表失败:', error);
    ElNotification({
      title: 'Error',
      message: '获取部门列表失败',
      type: 'error',
    });
  }
};

// 根据部门ID获取部门名称
const getDepartmentName = (departmentId) => {
  if (!departmentId || !departmentList.value.length) {
    return '未分配';
  }
  const department = departmentList.value.find(dept => dept.id === departmentId);
  return department ? department.name : '未知部门';
};


const addEmp=()=>{//添加员工
  console.log('=== 打开添加员工对话框 ===');

  // 清空表单
  Object.assign(form2, {
    name: '',
    gender: '',
    age: '',
    phone: '',
    email: '',
    departmentId: null,  // 改为null
    position: '',
    hireDate: '',
    status: 1
  });

  console.log('表单重置后:', form2);

  // 确保部门列表已加载
  console.log('当前部门列表长度:', departmentList.value.length);
  if (departmentList.value.length === 0) {
    console.log('部门列表为空，重新加载...');
    getDepartmentList();
  } else {
    console.log('部门列表已存在:', departmentList.value);
  }

  dialogVisible2.value=true;
}


const submit2=async()=>{
  console.log('=== 开始提交添加员工 ===');
  console.log('表单数据:', form2);
  console.log('姓名:', form2.name);
  console.log('性别:', form2.gender);
  console.log('手机号:', form2.phone);
  console.log('邮箱:', form2.email);
  console.log('部门ID:', form2.departmentId, '(类型:', typeof form2.departmentId, ')');

  // 验证必填字段 - 修复部门ID验证逻辑
  if (!form2.name || !form2.gender || !form2.phone || !form2.email ||
      form2.departmentId === '' || form2.departmentId === null || form2.departmentId === undefined) {
    console.log('=== 验证失败 ===');
    if (!form2.name) console.log('姓名为空');
    if (!form2.gender) console.log('性别为空');
    if (!form2.phone) console.log('手机号为空');
    if (!form2.email) console.log('邮箱为空');
    if (form2.departmentId === '' || form2.departmentId === null || form2.departmentId === undefined) {
      console.log('部门ID为空:', form2.departmentId);
    }

    ElNotification({
      title: 'Error',
      message: '请填写所有必填字段',
      type: 'error',
    });
    return;
  }

  console.log('=== 验证通过，准备提交 ===');
  console.log('添加员工信息:', form2);

  // 格式化日期
  const submitData = { ...form2 };
  if (submitData.hireDate) {
    // 将日期转换为 YYYY-MM-DD 格式
    const date = new Date(submitData.hireDate);
    submitData.hireDate = date.toISOString().split('T')[0];
  }

  console.log('格式化后的数据:', submitData);

  try {
    const data = await post("/addEmp", submitData);
    if(data.code==200){
      dialogVisible2.value=false;
      getData();
      ElNotification({
        title: 'Success',
        message: data.message,
        type: 'success',
      });
    }else{
      ElNotification({
        title: 'Error',
        message: data.message,
        type: 'error',
      });
    }
  } catch (error) {
    console.error('添加员工失败:', error);
    ElNotification({
      title: 'Error',
      message: '添加员工失败',
      type: 'error',
    });
  }
}

const handleCurrentChange = (val) => {//点击角标把当前页数赋值给currentPage
    currentPage.value=val;
    getData();
}


const search=async()=>{ 
    const data=await get("/selectLikeUsername",{username:username.value});
      tableData.value=data.data;
}

const handleEdit = (index, row) => {
  console.log('=== 开始编辑员工 ===');
  console.log('原始员工数据:', row);

  // 将员工信息复制到form2中
  Object.assign(form2, {
    id: row.id,
    name: row.name,
    gender: row.gender,
    age: row.age,
    phone: row.phone,
    email: row.email,
    departmentId: row.departmentId,
    position: row.position,
    hireDate: row.hireDate,
    status: row.status
  });

  console.log('复制后的form2数据:', form2);

  // 确保部门列表已加载
  if (departmentList.value.length === 0) {
    console.log('部门列表为空，重新加载...');
    getDepartmentList();
  } else {
    console.log('部门列表已存在，数量:', departmentList.value.length);
  }

  dialogVisible.value=true;
  console.log('=== 编辑对话框已打开 ===');
}


const submit = () => {
  // 验证必填字段
  if (!form2.name || !form2.gender || !form2.phone || !form2.email || !form2.departmentId) {
    ElNotification({
      title: 'Error',
      message: '请填写所有必填字段',
      type: 'error',
    });
    return;
  }
  updateUser();
}


//编辑用户
const updateUser=async()=>{
  console.log('更新员工信息:', form2);

  // 格式化日期
  const submitData = { ...form2 };
  if (submitData.hireDate) {
    // 将日期转换为 YYYY-MM-DD 格式
    const date = new Date(submitData.hireDate);
    submitData.hireDate = date.toISOString().split('T')[0];
  }

  console.log('格式化后的数据:', submitData);

  try {
    const data=await post("/updateEmployee",submitData);
    if(data.code==200){
        getData();
        dialogVisible.value=false;
             ElNotification({
        title: 'Success',
        message: data.message,
        type: 'success',
      });

    }else{
        ElNotification({
        title: 'Error',
        message: data.message,
        type: 'error',
      });
    }
  } catch (error) {
    console.error('更新员工失败:', error);
    ElNotification({
      title: 'Error',
      message: '更新员工失败',
      type: 'error',
    });
  }
}


const handleDelete = async(index, row) => {

const data=await get("/deleteEmp",{id:row.id});

if(data.code==200){
   tableData.value.splice(index, 1);
    getData();
    dialogVisible.value=false;
         ElNotification({
	    title: 'Success',
	    message: data.message,
	    type: 'success',
	  });
}else{
    ElNotification({
	    title: 'Error',
	    message: data.message,
	    type: 'error',
	  });

}
}

const tableData = ref([]);


const getData = async()=>{
const data=await get("/getUserInfo",{currentPage:currentPage.value});
tableData.value=data.data;
totalNum.value=data.code;
setTimeout(()=>{
  loading.value=false;
},300);
}
onMounted(async()=>{//获取数据,赋值给tableData
  getData();
  getDepartmentList(); // 获取部门列表
})
</script>4y
