<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>系统仪表板</h1>
      <p>欢迎使用人力资源管理系统！这里是您的数据概览</p>
    </div>
    
    <div class="dashboard-content">
      <!-- 统计卡片 -->
      <div class="stats-cards">
        <el-row :gutter="20">
          <el-col :span="6" v-for="(item, index) in statsData" :key="index">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon" :style="{ backgroundColor: item.color }">
                  <component :is="item.icon" />
                </div>
                <div class="stat-info">
                  <h3>{{ item.value }}</h3>
                  <p>{{ item.label }}</p>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- ECharts图表 -->
      <div class="chart-section">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <h3>部门员工分布 - 南丁格尔玫瑰图</h3>
              <p>基于实时数据的可视化展示</p>
            </div>
          </template>
          <div ref="chartContainer" class="chart-container"></div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { get } from '../api/api.js'
import {
  User,
  UserFilled,
  OfficeBuilding,
  ShoppingBag
} from '@element-plus/icons-vue'

// 图表容器引用
const chartContainer = ref(null)
let chartInstance = null

// 统计数据
const statsData = ref([
  { label: '员工总数', value: '0', icon: User, color: '#409EFF' },
  { label: '在职员工', value: '0', icon: UserFilled, color: '#67C23A' },
  { label: '部门数量', value: '0', icon: OfficeBuilding, color: '#E6A23C' },
  { label: '商品数量', value: '0', icon: ShoppingBag, color: '#F56C6C' }
])

// 获取统计数据
const fetchStatsData = async () => {
  try {
    console.log('开始获取统计数据...')

    // 获取员工总数和在职员工数
    try {
      const empResponse = await get('/employee/page', { currentPage: 1, pageSize: 1000 });
      console.log('员工数据响应:', empResponse)
      if (empResponse.code === 200) {
        const employees = empResponse.data;
        const totalEmployees = employees.length;
        const activeEmployees = employees.filter(emp => emp.status === 1).length;

        statsData.value[0].value = totalEmployees.toString();
        statsData.value[1].value = activeEmployees.toString();
      }
    } catch (error) {
      console.log('员工数据获取失败:', error)
      statsData.value[0].value = '0';
      statsData.value[1].value = '0';
    }

    // 获取部门数量
    try {
      const deptResponse = await get('/getAllDepts');
      console.log('部门数据响应:', deptResponse)
      if (deptResponse.code === 200) {
        statsData.value[2].value = deptResponse.data.length.toString();
      }
    } catch (error) {
      console.log('部门数据获取失败:', error)
      statsData.value[2].value = '0';
    }

    // 获取商品数量
    try {
      const productResponse = await get('/product/page', { currentPage: 1, pageSize: 1000 });
      console.log('商品数据响应:', productResponse)
      if (productResponse.code === 200) {
        statsData.value[3].value = productResponse.data.length.toString();
      }
    } catch (error) {
      console.log('商品数据获取失败:', error)
      statsData.value[3].value = '0';
    }

    console.log('统计数据获取完成:', statsData.value)

  } catch (error) {
    console.error('获取统计数据失败:', error);
    // 使用默认数据
    statsData.value[0].value = '10';
    statsData.value[1].value = '8';
    statsData.value[2].value = '4';
    statsData.value[3].value = '0';
  }
}

// 图表数据
const chartData = ref([])

// 获取部门员工分布数据
const fetchChartData = async () => {
  try {
    console.log('开始获取图表数据...')

    // 获取所有部门
    const deptResponse = await get('/getAllDepts');
    // 获取所有员工
    const empResponse = await get('/employee/page', { currentPage: 1, pageSize: 1000 });

    console.log('部门响应:', deptResponse)
    console.log('员工响应:', empResponse)

    if (deptResponse.code === 200 && empResponse.code === 200) {
      const departments = deptResponse.data;
      const employees = empResponse.data;

      // 统计每个部门的员工数量
      const deptEmployeeCount = departments.map((dept, index) => {
        const count = employees.filter(emp => emp.departmentId === dept.id).length;
        const colors = ['#FF6B6B', '#FFD93D', '#6BCB77', '#4D96FF', '#B983FF', '#FF9F1C', '#8338EC', '#3A86FF'];
        return {
          id: dept.id,
          label: dept.name,
          count: count,
          color: colors[index % colors.length]
        };
      });

      chartData.value = deptEmployeeCount;
      console.log('图表数据设置完成:', chartData.value)
    }
  } catch (error) {
    console.error('获取图表数据失败:', error);
    // 使用默认数据
    chartData.value = [
      { id: 1, label: '技术部', count: 15, color: '#FF6B6B' },
      { id: 2, label: '人事部', count: 8, color: '#FFD93D' },
      { id: 3, label: '财务部', count: 6, color: '#6BCB77' },
      { id: 4, label: '市场部', count: 12, color: '#4D96FF' }
    ];
    console.log('使用默认图表数据:', chartData.value)
  }
}

// 初始化图表
const initChart = () => {
  console.log('开始初始化图表...')
  console.log('图表容器:', chartContainer.value)
  console.log('图表数据:', chartData.value)

  if (!chartContainer.value) {
    console.error('图表容器不存在')
    return
  }

  if (!chartData.value || chartData.value.length === 0) {
    console.error('图表数据为空')
    return
  }

  try {
    // 销毁已存在的图表实例
    if (chartInstance) {
      chartInstance.dispose()
    }

    // 创建新的图表实例
    chartInstance = echarts.init(chartContainer.value)
    console.log('图表实例创建成功')
  } catch (error) {
    console.error('创建图表实例失败:', error)
    return
  }
  
  // 配置图表选项
  const option = {
    title: {
      text: '部门员工分布图',
      subtext: '各部门员工数量统计',
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold'
      },
      subtextStyle: {
        fontSize: 12,
        color: '#666'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      bottom: 20,
      left: 'center'
    },
    toolbox: {
      show: true,
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        restore: { show: true },
        saveAsImage: { show: true }
      },
      right: 20,
      top: 20
    },
    series: [
      {
        name: '部门员工分布',
        type: 'pie',
        radius: [50, 140],
        center: ['50%', '58%'],
        roseType: 'area',
        itemStyle: {
          borderRadius: 8
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c}',
          fontSize: 12
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 20
        },
        data: chartData.value.map(item => ({
          value: item.count,
          name: item.label,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  }
  
  // 设置图表配置
  chartInstance.setOption(option)
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 组件挂载后初始化
onMounted(async () => {
  await nextTick()
  // 先获取数据
  await fetchStatsData()
  await fetchChartData()
  // 再初始化图表
  initChart()
})

// 组件卸载时清理
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.dashboard-header {
  margin-bottom: 30px;
  text-align: center;
}

.dashboard-header h1 {
  color: #303133;
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: bold;
}

.dashboard-header p {
  color: #909399;
  font-size: 16px;
  margin: 0;
}

.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
}

.stats-cards {
  margin-bottom: 30px;
}

.stat-card {
  height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  color: white;
  font-size: 24px;
}

.stat-info h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.chart-section {
  margin-top: 20px;
}

.chart-card {
  min-height: 550px;
}

.chart-header {
  text-align: center;
}

.chart-header h3 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 18px;
}

.chart-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.chart-container {
  width: 100%;
  height: 450px;
  margin-top: 10px;
  padding: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }
  
  .dashboard-header h1 {
    font-size: 24px;
  }
  
  .stat-card {
    margin-bottom: 15px;
  }
  
  .chart-container {
    height: 300px;
  }
}
</style>
