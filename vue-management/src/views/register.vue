<template>
  <div class="form-container">
    <el-card class="form-card">
      <template #header>
        <div class="card-header">
          <h2>用户注册</h2>
          <p>创建您的账户</p>
        </div>
      </template>

      <el-form :model="form" :rules="rules" ref="formRef" label-width="auto" class="form">
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="form.username"
            placeholder="请输入用户名"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email" required>
          <el-input
            v-model="form.email"
            type="email"
            placeholder="请输入邮箱地址"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入密码（至少6位，包含字母和数字）"
            show-password
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="form.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            show-password
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item class="btn-container">
          <el-button
            type="primary"
            @click="register"
            size="large"
            :loading="loading"
            :disabled="loading"
          >
            {{ loading ? '注册中...' : '立即注册' }}
          </el-button>
        </el-form-item>

        <div class="login-link">
          <el-link type="primary" @click="goToLogin">
            已有账号？返回登录
          </el-link>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { useRouter } from "vue-router";
import { get, post } from '../api/api.js';
import { ElNotification } from 'element-plus'

const router = useRouter();
const formRef = ref();
const loading = ref(false);
const form = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
});

// 表单验证规则
const rules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入邮箱地址'));
        } else if (!validateEmail(value)) {
          callback(new Error('请输入正确的邮箱格式'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码至少需要6个字符', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入密码'));
        } else if (!validatePassword(value)) {
          callback(new Error('密码必须包含字母和数字'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请再次输入密码'));
        } else if (value !== form.password) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
});

// 邮箱格式验证函数
const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// 密码强度验证函数
const validatePassword = (password) => {
  // 密码至少6位，包含字母和数字
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,}$/;
  return passwordRegex.test(password);
};

//注册
const register = async () => {
  // 使用表单验证
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
  } catch (error) {
    ElNotification({
      title: 'Error',
      message: '请检查表单输入',
      type: 'error',
    });
    return;
  }

  loading.value = true;

  try {
    const data = await post("/register", form);

    if(data.code == 200){
      // 重置表单
      if (formRef.value) {
        formRef.value.resetFields();
      }

      ElNotification({
        title: 'Success',
        message: data.message || '注册成功！即将跳转到登录页面',
        type: 'success',
        duration: 3000
      });

      // 注册成功后延迟跳转到登录页面
      setTimeout(() => {
        router.push('/');
      }, 2000);

    } else {
      ElNotification({
        title: 'Error',
        message: data.message || '注册失败，请重试',
        type: 'error',
      });
    }
  } catch (error) {
    console.error('注册请求失败:', error);
    ElNotification({
      title: 'Error',
      message: '网络错误，请稍后重试',
      type: 'error',
    });
  } finally {
    loading.value = false;
  }

}
// 返回登录页面
const goToLogin = () => {
  router.push('/');
}






</script>

<style scoped>
/* 背景图片和整体美化 */
.form-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: url('@/assets/images/login-bg.png') no-repeat center center fixed;
  background-size: cover;
  font-family: 'Arial', sans-serif; /* 优雅的字体 */
}


/* 表单卡片样式，背景半透明 */
.form-card {
  width: 600px;
  padding: 30px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.85); /* 半透明白色背景 */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2); /* 阴影 */
  backdrop-filter: blur(10px); /* 背景模糊效果 */
  border: 1px solid rgba(0, 0, 0, 0.1); /* 边框颜色 */
}

/* 卡片标题样式 */
.card-header {
  text-align: center;
  margin-bottom: 20px;
}

.card-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
  font-weight: bold;
}

.card-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 表单样式 */
.form {
  width: 100%;
  color: #333;
}

/* 登录链接样式 */
.login-link {
  text-align: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 输入框和按钮样式 */
.el-input, .el-button {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 每个表单项之间的间距 */
.el-form-item {
  margin-bottom: 20px;
}

/* 输入框样式 */
.el-input {
  width: 100%;
  font-size: 16px;
  padding: 12px;
  border: 1px solid #ddd;
  background-color: #f9f9f9;
  color: #555;
}

/* 按钮样式 */
.el-button {
  width: 150px;
  height: 45px;
  background-color: #4a90e2; /* 蓝色 */
  color: white;
  border: none;
  font-size: 16px;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.el-button:hover {
  background-color: #357ab7; /* 深蓝色 */
  transform: translateY(-3px); /* 按钮悬浮效果 */
}

/* 按钮容器 */
.btn-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 输入框的聚焦效果 */
.el-input.is-focus {
  border-color: #4a90e2;
  box-shadow: 0 0 5px rgba(74, 144, 226, 0.5);
}

/* 表单标签样式 */
.el-form-item__label {
  font-size: 16px;
  font-weight: bold;
  color: #444;
}

/* 美化表单容器的边框和阴影 */
.el-card {
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* 调整表单卡片间距 */
.el-form {
  margin-top: 20px;
}

/* 边框和背景颜色 */
.el-card {
  background: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
