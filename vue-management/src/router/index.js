import { createRouter, createWebHistory } from 'vue-router'
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'login',
      component: () => import('../views/login.vue')
    },
    {
      path: '/main',
      name: 'index',
      component: () => import('../views/index.vue'),
      children:[
        {
          path: '',
          name: 'dashboard',
          component: () => import('../views/Dashboard.vue')
        },
        {
          path: '/main/dashboard',
          name: 'dashboardAlias',
          component: () => import('../views/Dashboard.vue')
        },
        {
          path: '/main/userList',
          name: 'userList',
          component: () => import('../views/user/userList.vue')
        },
         {
          path: '/main/empList',
          name: 'empList',
          component: () => import('../views/emp/empList.vue')
        },
        {
          path: '/main/deptList',
          name: 'deptList',
          component: () => import('../views/dept/deptList.vue')
        },
        {
          path: '/main/productList',
          name: 'productList',
          component: () => import('../views/product/productList.vue')
        },

      ]
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/register.vue')
    },
  ],
})

export default router
