import { get, post } from './api'

/**
 * 商品管理相关API
 */

// 获取所有商品（分页）
export const getAllProducts = (currentPage = 1) => {
  return get('/getAllProducts', { currentPage })
}

// 根据ID获取商品详情
export const getProductById = (productId) => {
  return get('/getProductById', { productId })
}

// 添加商品
export const addProduct = (productData) => {
  return post('/addProduct', productData)
}

// 更新商品
export const updateProduct = (productData) => {
  return post('/updateProduct', productData)
}

// 删除商品
export const deleteProduct = (productId) => {
  return post('/deleteProduct', { productId })
}

// 根据商品名称搜索
export const searchProductByName = (productName) => {
  return get('/searchProductByName', { productName })
}

// 根据价格区间搜索
export const searchProductByPrice = (minPrice, maxPrice) => {
  return get('/searchProductByPrice', { minPrice, maxPrice })
}

// 获取库存不足的商品
export const getLowStockProducts = (threshold = 10) => {
  return get('/getLowStockProducts', { threshold })
}

// 更新商品库存
export const updateProductStock = (productId, quantity) => {
  return post('/updateProductStock', { productId, quantity })
}

// 批量删除商品
export const batchDeleteProducts = (productIds) => {
  return post('/batchDeleteProducts', { productIds })
}

// 导出商品数据
export const exportProducts = () => {
  return get('/exportProducts')
}

// 商品统计信息
export const getProductStatistics = () => {
  return get('/getProductStatistics')
}
