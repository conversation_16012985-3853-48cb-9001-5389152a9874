import axios from 'axios';

// 创建一个Axios实例
const apiClient = axios.create({
  baseURL: '/api', // 使用代理路径
  headers: {
    'Content-Type': 'application/json', // 默认的请求头
    'token': localStorage.getItem('token') || '' // 初始token
  },
  timeout: 10000, // 设置超时时间
});


// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 每次请求时从 localStorage 获取最新的 token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['token'] = token;
    }
    return config;
  },
  (error) => {
    // 请求错误处理
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    // 后端返回的数据格式可以统一处理
    if (response.data.code === 401) {
      // 例如未登录或token过期
      alert('登录已过期，请重新登录');
      localStorage.removeItem('token');
      window.location.href = '/login'; // 跳转到登录页
    }
    return response.data;
  },
  (error) => {
    // 统一错误处理
    if (error.response) {
      // 服务器返回错误
      console.error('响应错误:', error.response.data);
      if (error.response.status === 401) {
        alert('登录已过期，请重新登录');
        localStorage.removeItem('token');
        window.location.href = '/login';
      }
    } else if (error.request) {
      // 请求发出但无响应
      console.error('无响应:', error.request);
    } else {
      console.error('请求错误:', error.message);
    }
    return Promise.reject(error);
  }
);

// 封装GET请求，参数通过查询字符串传递
export const get = async (url, params = {}) => {
  try {
    const response = await apiClient.get(url, { params }); // 传递参数
    return response; // 返回响应的数据
  } catch (error) {
    throw new Error(`GET请求失败: ${error.message}`);
  }
};

// POST请求
export const post = async (url, data, config = {}) => {
  try {
    const response = await apiClient.post(url, data, config);
    return response; // 返回数据
  } catch (error) {
    throw new Error(`POST请求错误: ${error.message}`);
  }
};

export default apiClient; // 导出Axios实例