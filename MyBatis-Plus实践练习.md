# MyBatis Plus 实践练习 - 清风商城项目

## 🎯 练习目标
通过一系列实践练习，掌握 MyBatis Plus 在实际项目中的应用，提升数据库操作技能。

---

## 📋 练习清单

### 基础练习（必做）
- [x] 练习1：基础 CRUD 操作
- [x] 练习2：条件查询
- [x] 练习3：分页查询
- [x] 练习4：批量操作

### 进阶练习（推荐）
- [x] 练习5：复杂关联查询
- [x] 练习6：事务管理
- [x] 练习7：自定义 SQL
- [x] 练习8：性能优化

### 高级练习（挑战）
- [x] 练习9：动态查询
- [x] 练习10：数据统计分析

---

## 🚀 练习1：基础 CRUD 操作

### 任务描述
实现商品的基本增删改查功能

### 练习代码

```java
@Test
public void testBasicCRUD() {
    // 1. 新增商品
    Product product = new Product();
    product.setName("MacBook Pro");
    product.setCategoryId(1001L);
    product.setPrice(new BigDecimal("12999.00"));
    product.setDescription("苹果笔记本电脑");
    product.setImageUrl("http://example.com/macbook.jpg");
    product.setStatus(1);
    
    boolean saved = productService.save(product);
    System.out.println("保存结果：" + saved);
    System.out.println("商品ID：" + product.getId());
    
    // 2. 根据ID查询
    Product queryProduct = productService.getById(product.getId());
    System.out.println("查询结果：" + queryProduct);
    
    // 3. 更新商品
    queryProduct.setPrice(new BigDecimal("11999.00"));
    boolean updated = productService.updateById(queryProduct);
    System.out.println("更新结果：" + updated);
    
    // 4. 删除商品
    boolean removed = productService.removeById(product.getId());
    System.out.println("删除结果：" + removed);
}
```

### 预期结果
- 成功创建商品并获得自增ID
- 能够根据ID查询到商品信息
- 成功更新商品价格
- 成功删除商品

---

## 🔍 练习2：条件查询

### 任务描述
使用 QueryWrapper 和 LambdaQueryWrapper 实现各种条件查询

### 练习代码

```java
@Test
public void testConditionalQuery() {
    // 1. 查询价格在100-1000之间的商品
    QueryWrapper<Product> wrapper1 = new QueryWrapper<>();
    wrapper1.between("price", 100, 1000)
            .eq("status", 1)
            .orderByDesc("create_time");
    List<Product> products1 = productService.list(wrapper1);
    System.out.println("价格在100-1000之间的商品：" + products1.size() + "个");
    
    // 2. 使用Lambda查询名称包含"手机"的商品
    LambdaQueryWrapper<Product> wrapper2 = new LambdaQueryWrapper<>();
    wrapper2.like(Product::getName, "手机")
            .eq(Product::getStatus, 1)
            .orderByAsc(Product::getPrice);
    List<Product> products2 = productService.list(wrapper2);
    System.out.println("名称包含'手机'的商品：" + products2.size() + "个");
    
    // 3. 复杂条件查询：价格大于500或者分类ID为1001的上架商品
    LambdaQueryWrapper<Product> wrapper3 = new LambdaQueryWrapper<>();
    wrapper3.eq(Product::getStatus, 1)
            .and(w -> w.gt(Product::getPrice, 500)
                      .or()
                      .eq(Product::getCategoryId, 1001L));
    List<Product> products3 = productService.list(wrapper3);
    System.out.println("复杂条件查询结果：" + products3.size() + "个");
    
    // 4. 统计查询
    LambdaQueryWrapper<Product> wrapper4 = new LambdaQueryWrapper<>();
    wrapper4.eq(Product::getStatus, 1);
    long count = productService.count(wrapper4);
    System.out.println("上架商品总数：" + count);
}
```

### 练习要点
- 掌握 `eq`、`like`、`between`、`gt`、`lt` 等条件方法
- 理解 `and`、`or` 逻辑组合
- 学会使用 `orderBy` 排序
- 掌握 `count` 统计查询

---

## 📄 练习3：分页查询

### 任务描述
实现商品的分页查询功能

### 练习代码

```java
@Test
public void testPagination() {
    // 1. 基础分页查询
    Page<Product> page1 = new Page<>(1, 5);  // 第1页，每页5条
    Page<Product> result1 = productService.page(page1);
    
    System.out.println("=== 基础分页查询 ===");
    System.out.println("当前页：" + result1.getCurrent());
    System.out.println("每页大小：" + result1.getSize());
    System.out.println("总记录数：" + result1.getTotal());
    System.out.println("总页数：" + result1.getPages());
    System.out.println("当前页数据：");
    result1.getRecords().forEach(System.out::println);
    
    // 2. 带条件的分页查询
    Page<Product> page2 = new Page<>(1, 3);
    LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(Product::getStatus, 1)
           .orderByDesc(Product::getCreateTime);
    
    Page<Product> result2 = productService.page(page2, wrapper);
    
    System.out.println("\n=== 带条件的分页查询 ===");
    System.out.println("上架商品分页结果：");
    result2.getRecords().forEach(product -> 
        System.out.println(product.getName() + " - " + product.getPrice())
    );
}
```

### 注意事项
- 确保已配置分页插件
- 页码从1开始，不是从0开始
- 分页查询会自动添加 LIMIT 子句

---

## 📦 练习4：批量操作

### 任务描述
实现商品的批量插入、更新、删除操作

### 练习代码

```java
@Test
public void testBatchOperations() {
    // 1. 批量插入
    List<Product> products = Arrays.asList(
        new Product("iPhone 15", 1001L, new BigDecimal("7999.00"), "苹果手机", "url1"),
        new Product("Samsung S24", 1001L, new BigDecimal("6999.00"), "三星手机", "url2"),
        new Product("Huawei P60", 1001L, new BigDecimal("5999.00"), "华为手机", "url3")
    );
    
    boolean batchSaved = productService.saveBatch(products);
    System.out.println("批量插入结果：" + batchSaved);
    
    // 2. 批量更新
    products.forEach(product -> product.setStatus(1));
    boolean batchUpdated = productService.updateBatchById(products);
    System.out.println("批量更新结果：" + batchUpdated);
    
    // 3. 批量删除
    List<Long> ids = products.stream()
                            .map(Product::getId)
                            .collect(Collectors.toList());
    boolean batchRemoved = productService.removeByIds(ids);
    System.out.println("批量删除结果：" + batchRemoved);
    
    // 4. 批量插入或更新（saveOrUpdateBatch）
    List<Product> newProducts = Arrays.asList(
        new Product("iPad Pro", 1002L, new BigDecimal("8999.00"), "苹果平板", "url4"),
        new Product("Surface Pro", 1002L, new BigDecimal("7999.00"), "微软平板", "url5")
    );
    
    boolean saveOrUpdated = productService.saveOrUpdateBatch(newProducts);
    System.out.println("批量插入或更新结果：" + saveOrUpdated);
}
```

### 性能提示
- 批量操作比循环单个操作效率更高
- 可以设置批量大小：`saveBatch(list, 1000)`
- 大数据量时建议分批处理

---

## 🔗 练习5：复杂关联查询

### 任务描述
实现商品与分类、库存的关联查询

### 练习代码

```java
@Test
public void testComplexQuery() {
    // 1. 查询商品及其分类信息
    List<Map<String, Object>> productsWithCategory = productMapper.selectProductsWithCategory();
    System.out.println("=== 商品及分类信息 ===");
    productsWithCategory.forEach(map -> {
        System.out.println("商品：" + map.get("name") + 
                          "，分类：" + map.get("category_name") + 
                          "，价格：" + map.get("price"));
    });
    
    // 2. 查询商品及其库存信息
    List<Map<String, Object>> productsWithStock = productMapper.selectProductsWithStock();
    System.out.println("\n=== 商品及库存信息 ===");
    productsWithStock.forEach(map -> {
        System.out.println("商品：" + map.get("name") + 
                          "，库存：" + map.get("quantity") + 
                          "，状态：" + map.get("stock_status"));
    });
    
    // 3. 查询库存不足的商品
    List<Map<String, Object>> lowStockProducts = stockMapper.selectLowStockProducts();
    System.out.println("\n=== 库存不足的商品 ===");
    lowStockProducts.forEach(map -> {
        System.out.println("商品：" + map.get("name") + 
                          "，当前库存：" + map.get("quantity") + 
                          "，最低库存：" + map.get("min_stock"));
    });
}
```

### 学习要点
- 掌握 JOIN 查询的使用
- 理解 LEFT JOIN 和 INNER JOIN 的区别
- 学会使用 CASE WHEN 进行条件判断

---

## 💰 练习6：事务管理

### 任务描述
实现带事务的商品添加功能（同时创建库存记录）

### 练习代码

```java
@Service
@Transactional(rollbackFor = Exception.class)
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Autowired
    private StockService stockService;

    @Override
    @Transactional
    public boolean addProductWithStock(Product product, Integer initialStock) {
        try {
            // 1. 保存商品信息
            boolean productSaved = save(product);
            if (!productSaved) {
                throw new RuntimeException("商品保存失败");
            }
            
            // 2. 创建库存记录
            Stock stock = new Stock();
            stock.setProductId(product.getId());
            stock.setQuantity(initialStock);
            stock.setMinStock(10);  // 默认最低库存
            
            boolean stockSaved = stockService.save(stock);
            if (!stockSaved) {
                throw new RuntimeException("库存记录创建失败");
            }
            
            // 模拟异常情况（用于测试事务回滚）
            if (product.getName().contains("ERROR")) {
                throw new RuntimeException("模拟异常，触发事务回滚");
            }
            
            return true;
            
        } catch (Exception e) {
            System.err.println("添加商品失败：" + e.getMessage());
            throw e;  // 重新抛出异常，触发事务回滚
        }
    }
}

// 测试代码
@Test
public void testTransaction() {
    // 1. 正常情况测试
    Product product1 = new Product("正常商品", 1001L, new BigDecimal("99.00"), "测试商品", "url");
    boolean result1 = productService.addProductWithStock(product1, 100);
    System.out.println("正常添加结果：" + result1);
    
    // 2. 异常情况测试（事务回滚）
    Product product2 = new Product("ERROR商品", 1001L, new BigDecimal("99.00"), "测试商品", "url");
    try {
        productService.addProductWithStock(product2, 100);
    } catch (Exception e) {
        System.out.println("异常捕获：" + e.getMessage());
        
        // 验证事务回滚：商品和库存都不应该被保存
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(Product::getName, "ERROR");
        long count = productService.count(wrapper);
        System.out.println("ERROR商品数量（应该为0）：" + count);
    }
}
```

### 事务要点
- 使用 `@Transactional` 注解
- 设置 `rollbackFor = Exception.class` 确保所有异常都回滚
- 理解事务的 ACID 特性
- 掌握事务传播机制

---

## 📊 练习7：数据统计分析

### 任务描述
实现商品销售数据的统计分析

### 练习代码

```java
@Test
public void testDataAnalysis() {
    // 1. 按分类统计商品数量
    QueryWrapper<Product> wrapper1 = new QueryWrapper<>();
    wrapper1.select("category_id, COUNT(*) as product_count")
            .eq("status", 1)
            .groupBy("category_id");
    List<Map<String, Object>> categoryStats = productMapper.selectMaps(wrapper1);
    
    System.out.println("=== 按分类统计商品数量 ===");
    categoryStats.forEach(map -> {
        System.out.println("分类ID：" + map.get("category_id") + 
                          "，商品数量：" + map.get("product_count"));
    });
    
    // 2. 价格区间分析
    QueryWrapper<Product> wrapper2 = new QueryWrapper<>();
    wrapper2.select(
        "CASE " +
        "WHEN price < 100 THEN '0-100' " +
        "WHEN price < 500 THEN '100-500' " +
        "WHEN price < 1000 THEN '500-1000' " +
        "ELSE '1000+' END as price_range, " +
        "COUNT(*) as count"
    ).eq("status", 1)
     .groupBy("price_range");
    
    List<Map<String, Object>> priceRangeStats = productMapper.selectMaps(wrapper2);
    
    System.out.println("\n=== 价格区间分析 ===");
    priceRangeStats.forEach(map -> {
        System.out.println("价格区间：" + map.get("price_range") + 
                          "，商品数量：" + map.get("count"));
    });
    
    // 3. 商品价格统计
    QueryWrapper<Product> wrapper3 = new QueryWrapper<>();
    wrapper3.select("AVG(price) as avg_price, MAX(price) as max_price, MIN(price) as min_price")
            .eq("status", 1);
    Map<String, Object> priceStats = productMapper.selectOne(wrapper3);
    
    System.out.println("\n=== 商品价格统计 ===");
    System.out.println("平均价格：" + priceStats.get("avg_price"));
    System.out.println("最高价格：" + priceStats.get("max_price"));
    System.out.println("最低价格：" + priceStats.get("min_price"));
}
```

---

## 🎯 练习总结

### 完成检查清单
- [ ] 能够熟练使用基础 CRUD 操作
- [ ] 掌握各种条件查询方法
- [ ] 理解分页查询的实现
- [ ] 会使用批量操作提高效率
- [ ] 能够编写复杂的关联查询
- [ ] 理解事务管理的重要性
- [ ] 掌握数据统计分析方法

### 下一步学习建议
1. **深入学习**：研究 MyBatis Plus 源码，理解其实现原理
2. **性能优化**：学习 SQL 优化技巧，提高查询效率
3. **实际应用**：在真实项目中应用所学知识
4. **持续关注**：关注 MyBatis Plus 的新版本和新特性

### 常见错误总结
1. **忘记配置分页插件**导致分页不生效
2. **字段名映射错误**导致查询失败
3. **事务注解使用不当**导致数据不一致
4. **批量操作数据量过大**导致内存溢出

**恭喜你完成了所有练习！继续加油，成为 MyBatis Plus 专家！** 🎉
