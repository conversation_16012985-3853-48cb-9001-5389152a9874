# 部门删除功能修复指南

## 🐛 问题描述

在进行部门删除时，删除失败，前端提示以下错误信息：
```
Error
删除部门失败：Invalid bound statement (not found): qidian.it.springboot.mapper.EmployeeMapper.selectByDepartmentId
```

## 🔍 问题分析

### 错误原因
1. **方法定义不匹配**: 在EmployeeMapper接口中定义了`selectByDepartmentId`方法
2. **缺少SQL实现**: 该方法没有对应的XML SQL实现
3. **Mybatis-Plus未自动生成**: 这不是BaseMapper的标准方法，Mybatis-Plus无法自动生成
4. **混合使用方式**: 混合使用了传统Mybatis和Mybatis-Plus的方式

### 技术背景
- **传统Mybatis**: 需要在XML中为每个方法提供SQL实现
- **Mybatis-Plus**: 提供BaseMapper的标准CRUD方法，自定义查询建议使用QueryWrapper
- **混合使用**: 容易导致方法找不到对应的SQL实现

## ✅ 修复方案

### 1. 移除自定义Mapper方法
**文件**: `EmployeeMapper.java`

#### 修改前
```java
/**
 * 根据部门ID查询员工列表
 * @param departmentId 部门ID
 * @return 员工列表
 */
List<Employee> selectByDepartmentId(@Param("departmentId") Long departmentId);
```

#### 修改后
```java
/**
 * 根据部门ID查询员工列表（使用Mybatis-Plus方式，不需要XML实现）
 * 可以直接使用BaseMapper的方法或者在Service层用QueryWrapper实现
 * @param departmentId 部门ID
 * @return 员工列表
 */
// List<Employee> selectByDepartmentId(@Param("departmentId") Long departmentId);
```

### 2. 使用Mybatis-Plus QueryWrapper方式
**文件**: `DepartmentServiceImpl.java`

#### 修改前
```java
// 检查该部门下是否有员工
List<Employee> employees = employeeMapper.selectByDepartmentId(id);
if (employees != null && employees.size() > 0) {
    return Result.fail("该部门下有员工,无法删除");
}
```

#### 修改后
```java
// 检查该部门下是否有员工（使用Mybatis-Plus的QueryWrapper）
QueryWrapper<Employee> queryWrapper = new QueryWrapper<>();
queryWrapper.eq("department_id", id);
long employeeCount = employeeMapper.selectCount(queryWrapper);

if (employeeCount > 0) {
    return Result.fail("该部门下有员工,无法删除");
}
```

### 3. 添加必要的导入
```java
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
```

## 🎯 修复原理

### Mybatis-Plus查询方式
```java
// 1. 创建查询条件构造器
QueryWrapper<Employee> queryWrapper = new QueryWrapper<>();

// 2. 添加查询条件
queryWrapper.eq("department_id", id);  // WHERE department_id = ?

// 3. 执行查询
long count = employeeMapper.selectCount(queryWrapper);  // 统计数量
List<Employee> list = employeeMapper.selectList(queryWrapper);  // 查询列表
```

### BaseMapper提供的方法
```java
// 基础查询方法
T selectById(Serializable id);                    // 根据ID查询
List<T> selectList(Wrapper<T> queryWrapper);      // 条件查询列表
Long selectCount(Wrapper<T> queryWrapper);        // 条件统计数量
IPage<T> selectPage(IPage<T> page, Wrapper<T> queryWrapper);  // 分页查询

// 基础操作方法
int insert(T entity);                             // 插入
int updateById(T entity);                         // 根据ID更新
int deleteById(Serializable id);                  // 根据ID删除
```

## 🚀 技术优势

### 1. 代码简洁性
- **修复前**: 需要定义接口方法 + XML SQL实现
- **修复后**: 直接使用QueryWrapper + BaseMapper方法

### 2. 类型安全
```java
// 类型安全的字段名
queryWrapper.eq("department_id", id);  // 编译期检查字段名

// 避免SQL拼写错误
// SELECT COUNT(*) FROM employee WHERE department_id = ?  // 自动生成
```

### 3. 维护便利
- **无需XML**: 不需要维护额外的XML文件
- **动态SQL**: QueryWrapper自动处理条件组合
- **性能优化**: Mybatis-Plus自动优化SQL

## 🔧 最佳实践

### 1. 简单查询使用QueryWrapper
```java
// 等值查询
QueryWrapper<Employee> wrapper = new QueryWrapper<>();
wrapper.eq("department_id", departmentId);

// 模糊查询
wrapper.like("name", name);

// 范围查询
wrapper.between("age", 18, 65);

// 组合条件
wrapper.eq("department_id", departmentId)
       .like("name", name)
       .gt("age", 18);
```

### 2. 复杂查询使用@Select注解
```java
@Select("SELECT e.*, d.name as dept_name FROM employee e " +
        "LEFT JOIN department d ON e.department_id = d.id " +
        "WHERE e.department_id = #{departmentId}")
List<EmployeeVO> selectEmployeeWithDept(@Param("departmentId") Long departmentId);
```

### 3. 超复杂查询使用XML
```xml
<select id="complexQuery" resultType="Employee">
    SELECT * FROM employee
    <where>
        <if test="departmentId != null">
            AND department_id = #{departmentId}
        </if>
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
    </where>
</select>
```

## 📊 性能对比

### 查询性能
| 方法 | SQL生成 | 类型安全 | 维护成本 | 性能 |
|------|---------|----------|----------|------|
| 自定义XML | 手动编写 | 低 | 高 | 高 |
| QueryWrapper | 自动生成 | 高 | 低 | 高 |
| @Select注解 | 手动编写 | 中 | 中 | 高 |

### 开发效率
- **修复前**: 定义接口 → 编写XML → 测试调试
- **修复后**: 直接使用QueryWrapper → 立即可用

## 🧪 测试验证

### 1. 功能测试
- **有员工的部门**: 删除时应该提示"该部门下有员工,无法删除"
- **无员工的部门**: 删除时应该成功删除
- **不存在的部门**: 删除时应该提示"部门不存在"

### 2. 性能测试
- **查询速度**: 验证QueryWrapper查询性能
- **内存使用**: 检查内存占用情况
- **并发测试**: 测试并发删除操作

### 3. 错误处理测试
- **数据库连接异常**: 测试网络异常情况
- **参数异常**: 测试空值和非法参数
- **权限异常**: 测试无权限操作

## 🔄 相关模块检查

### 其他可能的类似问题
检查其他Service类是否也存在类似的自定义Mapper方法调用：

1. **UserServiceImpl**: 检查是否有类似问题
2. **ProductServiceImpl**: 检查是否有类似问题
3. **其他Service**: 确保都使用正确的Mybatis-Plus方式

### 统一规范建议
1. **优先使用**: BaseMapper提供的标准方法
2. **简单查询**: 使用QueryWrapper构建条件
3. **复杂查询**: 使用@Select注解或XML
4. **避免混合**: 不要混合使用传统Mybatis和Mybatis-Plus

## 🎉 修复完成

部门删除功能已成功修复：

- ✅ **移除问题方法**: 注释掉有问题的自定义方法
- ✅ **使用QueryWrapper**: 采用Mybatis-Plus标准方式
- ✅ **添加必要导入**: 导入QueryWrapper类
- ✅ **编译成功**: 代码编译通过
- ✅ **服务器启动**: 功能可以正常使用

现在部门删除功能可以正常工作，不会再出现"Invalid bound statement"错误！🎊
