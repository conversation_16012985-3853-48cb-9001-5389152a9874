package qidian.it.springboot;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import qidian.it.springboot.entity.Admin;
import qidian.it.springboot.mapper.AdminMapper;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
public class AdminMapperTest {

    @Resource
    private AdminMapper adminMapper;

    @Test
    public void testSelectByUsername() {
        // 测试根据用户名查询
        Admin admin = adminMapper.selectByUsername("admin");
        System.out.println("查询结果: " + admin);
    }

    @Test
    public void testInsert() {
        // 测试插入新管理员
        Admin admin = new Admin();
        admin.setUsername("testuser");
        admin.setPassword("testpassword");
        admin.setEmail("<EMAIL>");
        
        int result = adminMapper.insert(admin);
        System.out.println("插入结果: " + result);
        System.out.println("插入后的ID: " + admin.getId());
    }

    @Test
    public void testSelectById() {
        // 测试根据ID查询（Mybatis-Plus提供的方法）
        Admin admin = adminMapper.selectById(1L);
        System.out.println("根据ID查询结果: " + admin);
    }

    @Test
    public void testGetAllInfo() {
        // 测试获取所有管理员信息
        List<Admin> adminList = adminMapper.getAllInfo();
        System.out.println("所有管理员信息: " + adminList);
    }

    @Test
    public void testUpdateById() {
        // 测试根据ID更新（Mybatis-Plus提供的方法）
        Admin admin = new Admin();
        admin.setId(1L);
        admin.setEmail("<EMAIL>");
        
        int result = adminMapper.updateById(admin);
        System.out.println("更新结果: " + result);
    }

    @Test
    public void testDeleteById() {
        // 测试根据ID删除（Mybatis-Plus提供的方法）
        // 注意：这个测试会实际删除数据，请谨慎使用
        // int result = adminMapper.deleteById(2L);
        // System.out.println("删除结果: " + result);
        System.out.println("删除测试已注释，避免误删数据");
    }
}
