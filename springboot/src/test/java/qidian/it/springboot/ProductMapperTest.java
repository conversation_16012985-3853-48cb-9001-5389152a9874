package qidian.it.springboot;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import qidian.it.springboot.entity.Product;
import qidian.it.springboot.mapper.ProductMapper;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
public class ProductMapperTest {

    @Resource
    private ProductMapper productMapper;

    @Test
    public void testSelectAll() {
        // 测试查询所有商品
        List<Product> products = productMapper.selectList(null);
        System.out.println("商品总数: " + products.size());
        for (Product product : products) {
            System.out.println(product);
        }
    }

    @Test
    public void testSelectById() {
        // 测试根据ID查询商品
        Product product = productMapper.selectById(1);
        System.out.println("根据ID查询结果: " + product);
    }

    @Test
    public void testInsert() {
        // 测试插入新商品
        Product product = new Product();
        product.setProductName("测试商品");
        product.setPrice(999.99);
        product.setStorageNum(100);
        product.setDescription("这是一个测试商品");
        
        int result = productMapper.insert(product);
        System.out.println("插入结果: " + result);
        System.out.println("插入后的ID: " + product.getProductId());
    }

    @Test
    public void testUpdateById() {
        // 测试根据ID更新商品
        Product product = new Product();
        product.setProductId(1);
        product.setPrice(8888.88);
        product.setStorageNum(45);
        
        int result = productMapper.updateById(product);
        System.out.println("更新结果: " + result);
    }

    @Test
    public void testDeleteById() {
        // 测试根据ID删除商品
        // 注意：这个测试会实际删除数据，请谨慎使用
        // int result = productMapper.deleteById(16);
        // System.out.println("删除结果: " + result);
        System.out.println("删除测试已注释，避免误删数据");
    }

    @Test
    public void testSelectByProductNameLike() {
        // 测试根据商品名称模糊查询
        List<Product> products = productMapper.selectByProductNameLike("iPhone");
        System.out.println("模糊查询结果: " + products.size() + " 条");
        for (Product product : products) {
            System.out.println(product.getProductName() + " - " + product.getPrice());
        }
    }

    @Test
    public void testSelectByPriceRange() {
        // 测试根据价格区间查询
        List<Product> products = productMapper.selectByPriceRange(1000.0, 5000.0);
        System.out.println("价格区间查询结果: " + products.size() + " 条");
        for (Product product : products) {
            System.out.println(product.getProductName() + " - " + product.getPrice());
        }
    }

    @Test
    public void testSelectLowStockProducts() {
        // 测试查询库存不足的商品
        List<Product> products = productMapper.selectLowStockProducts(50);
        System.out.println("库存不足商品: " + products.size() + " 条");
        for (Product product : products) {
            System.out.println(product.getProductName() + " - 库存: " + product.getStorageNum());
        }
    }

    @Test
    public void testGetAllProducts() {
        // 测试获取所有商品信息
        List<Product> products = productMapper.getAllProducts();
        System.out.println("所有商品信息: " + products.size() + " 条");
        for (Product product : products) {
            System.out.println(product);
        }
    }
}
