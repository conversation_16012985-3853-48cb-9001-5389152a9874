package qidian.it.springboot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import qidian.it.springboot.entity.Product;

import java.util.List;

/**
 * 商品Mapper接口
 * 继承Mybatis-Plus的BaseMapper，提供基础CRUD操作
 */
public interface ProductMapper extends BaseMapper<Product> {
    
    /**
     * 根据商品名称模糊查询
     * @param productName 商品名称
     * @return 商品列表
     */
    List<Product> selectByProductNameLike(String productName);
    
    /**
     * 根据价格区间查询商品
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @return 商品列表
     */
    List<Product> selectByPriceRange(Double minPrice, Double maxPrice);
    
    /**
     * 查询库存不足的商品
     * @param threshold 库存阈值
     * @return 商品列表
     */
    List<Product> selectLowStockProducts(Integer threshold);
    
    /**
     * 获取所有商品信息（分页）
     * @return 商品列表
     */
    List<Product> getAllProducts();
}
