package qidian.it.springboot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import qidian.it.springboot.entity.Employee;
import qidian.it.springboot.entity.User;

import java.util.List;

/**
 * 员工Mapper接口 - 使用Mybatis-Plus
 */
@Mapper
public interface EmployeeMapper extends BaseMapper<Employee> {

    /**
     * 分页查询员工列表
     * @param page 分页对象
     * @param name 员工姓名（模糊查询）
     * @param gender 性别
     * @param departmentId 部门ID
     * @return 分页结果
     */
    IPage<Employee> selectEmployeePage(Page<Employee> page,
                                      @Param("name") String name,
                                      @Param("gender") String gender,
                                      @Param("departmentId") Long departmentId);

    /**
     * 根据部门ID查询员工列表（使用Mybatis-Plus方式，不需要XML实现）
     * 可以直接使用BaseMapper的方法或者在Service层用QueryWrapper实现
     * @param departmentId 部门ID
     * @return 员工列表
     */
    // List<Employee> selectByDepartmentId(@Param("departmentId") Long departmentId);

    /**
     * 根据姓名模糊查询员工
     * @param name 员工姓名
     * @return 员工列表
     */
    List<Employee> selectByNameLike(@Param("name") String name);

    /**
     * 统计部门员工数量
     * @param departmentId 部门ID
     * @return 员工数量
     */
    Integer countByDepartmentId(@Param("departmentId") Long departmentId);

    /**
     * 批量更新员工状态
     * @param ids 员工ID列表
     * @param status 状态
     * @return 更新数量
     */
    Integer updateStatusByIds(@Param("ids") List<Long> ids, @Param("status") Byte status);

    // 保留原有的User相关方法（如果需要）
    User selectByUsername(String username);
    List<User> selectLikeUsername(String username);
    List<User> getAllInfo();
}