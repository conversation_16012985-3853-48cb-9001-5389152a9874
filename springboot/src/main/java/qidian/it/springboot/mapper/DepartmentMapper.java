package qidian.it.springboot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import qidian.it.springboot.entity.Department;

import java.util.List;

@Mapper
public interface DepartmentMapper extends BaseMapper<Department> {
    // MyBatis-Plus已经提供了基本的CRUD操作，这里只需要添加自定义方法

    /**
     * 根据部门名称查询部门
     * @param name 部门名称
     * @return 部门信息
     */
    Department selectByDeptName(String name);

    /**
     * 获取所有部门信息
     * @return 部门列表
     */
    List<Department> getAllInfo();
}