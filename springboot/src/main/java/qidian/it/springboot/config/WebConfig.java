package qidian.it.springboot.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类 - 配置静态资源映射和CORS
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * 配置静态资源处理器
     * 将上传文件的路径暴露为静态资源，使前端可以直接访问图片
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置商品图片的静态资源映射
        registry.addResourceHandler("/product_image/**")  // URL访问路径
                .addResourceLocations("file:D:/product_image/");  // 实际文件存储路径

        System.out.println("静态资源映射配置完成:");
        System.out.println("URL路径: /product_image/**");
        System.out.println("实际路径: file:D:/product_image/");
    }

    /**
     * 配置CORS跨域
     * 解决前端fetch访问静态资源的跨域问题
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);

        System.out.println("CORS跨域配置完成:");
        System.out.println("允许所有来源访问");
        System.out.println("允许所有HTTP方法");
    }
}
