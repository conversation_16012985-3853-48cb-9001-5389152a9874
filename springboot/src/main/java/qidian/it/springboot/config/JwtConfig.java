package qidian.it.springboot.config;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;

@Component
@ConfigurationProperties(prefix = "config.jwt")
public class JwtConfig {
    
    private String secret;
    private long expire;
    private String header;

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public long getExpire() {
        return expire;
    }

    public void setExpire(long expire) {
        this.expire = expire;
    }

    public String getHeader() {
        return header;
    }

    public void setHeader(String header) {
        this.header = header;
    }

    /**
     * 获取签名密钥
     */
    private SecretKey getSigningKey() {
        byte[] keyBytes = secret.getBytes(StandardCharsets.UTF_8);
        return Keys.hmacShaKeyFor(keyBytes);
    }

    /**
     * 生成token
     * @param subject 用户名或用户标识
     * @return JWT token
     */
    public String createToken(String subject) {
        Date nowDate = new Date();
        Date expireDate = new Date(nowDate.getTime() + expire * 1000); // 过期时间

        return Jwts.builder()
                .setHeaderParam("typ", "JWT")
                .setSubject(subject)
                .setIssuedAt(nowDate)
                .setExpiration(expireDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 获取token中注册信息
     * @param token JWT token
     * @return Claims对象
     */
    public Claims getTokenClaim(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            System.err.println("解析token失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 验证token是否过期失效
     * @param token JWT token
     * @return true表示已过期，false表示未过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            return expiration.before(new Date());
        } catch (Exception e) {
            return true; // 解析失败认为已过期
        }
    }

    /**
     * 获取token失效时间
     * @param token JWT token
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        Claims claims = getTokenClaim(token);
        return claims != null ? claims.getExpiration() : null;
    }

    /**
     * 获取用户名从token中
     * @param token JWT token
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = getTokenClaim(token);
        return claims != null ? claims.getSubject() : null;
    }

    /**
     * 获取jwt发布时间
     * @param token JWT token
     * @return 发布时间
     */
    public Date getIssuedAtDateFromToken(String token) {
        Claims claims = getTokenClaim(token);
        return claims != null ? claims.getIssuedAt() : null;
    }

    /**
     * 验证token是否有效
     * @param token JWT token
     * @return true表示有效，false表示无效
     */
    public boolean validateToken(String token) {
        try {
            Claims claims = getTokenClaim(token);
            return claims != null && !isTokenExpired(token);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public String toString() {
        return "JwtConfig{" +
                "secret='" + "***" + '\'' + // 不显示真实密钥
                ", expire=" + expire +
                ", header='" + header + '\'' +
                '}';
    }
}
