package qidian.it.springboot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import qidian.it.springboot.entity.Department;
import qidian.it.springboot.entity.Employee;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.mapper.DepartmentMapper;
import qidian.it.springboot.mapper.EmployeeMapper;
import qidian.it.springboot.service.DepartmentService;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class DepartmentServiceImpl extends ServiceImpl<DepartmentMapper, Department> implements DepartmentService {

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private EmployeeMapper employeeMapper;

    @Override
    public Result addDept(Department department) {
        try {
            // 验证必填字段
            if (Objects.isNull(department.getName()) || department.getName().trim().isEmpty()) {
                return Result.fail("部门名称不能为空");
            }

            // 检查部门名称是否已存在
            QueryWrapper<Department> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("name", department.getName().trim());
            Department existingDept = departmentMapper.selectOne(queryWrapper);

            if (Objects.nonNull(existingDept)) {
                return Result.fail("部门名称已存在");
            }

            // 处理部门信息
            department.setName(department.getName().trim());
            if (department.getDescription() != null) {
                department.setDescription(department.getDescription().trim());
            }

            // 使用MyBatis-Plus的save方法
            boolean result = this.save(department);
            if (result) {
                System.out.println("部门添加成功，ID: " + department.getId());
                return Result.success("添加成功");
            } else {
                return Result.fail("添加失败");
            }
        } catch (Exception e) {
            System.err.println("添加部门异常: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("添加部门失败：" + e.getMessage());
        }
    }

    @Override
    public Result getDeptInfo(Integer currentPage) {
        try {
            // 使用MyBatis-Plus的分页查询
            Page<Department> page = new Page<>(currentPage, 10); // 每页10条记录，提高显示效率

            // 查询所有部门信息
            Page<Department> departmentPage = departmentMapper.selectPage(page, null);

            System.out.println("部门总数：" + departmentPage.getTotal());
            System.out.println("当前页：" + departmentPage.getCurrent());
            System.out.println("每页大小：" + departmentPage.getSize());

            return Result.success((int) departmentPage.getTotal(), departmentPage.getRecords());
        } catch (Exception e) {
            System.err.println("获取部门信息异常: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("获取部门信息失败：" + e.getMessage());
        }
    }

    @Override
    public Result updateDept(Department department) {
        try {
            // 验证必填字段
            if (Objects.isNull(department.getId())) {
                return Result.fail("部门ID不能为空");
            }
            if (Objects.isNull(department.getName()) || department.getName().trim().isEmpty()) {
                return Result.fail("部门名称不能为空");
            }

            // 检查部门是否存在
            Department existingDept = departmentMapper.selectById(department.getId());
            if (Objects.isNull(existingDept)) {
                return Result.fail("部门不存在");
            }

            // 检查部门名称是否与其他部门重复
            QueryWrapper<Department> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("name", department.getName().trim())
                       .ne("id", department.getId());
            Department duplicateDept = departmentMapper.selectOne(queryWrapper);

            if (Objects.nonNull(duplicateDept)) {
                return Result.fail("部门名称已存在");
            }

            // 处理部门信息
            department.setName(department.getName().trim());
            if (department.getDescription() != null) {
                department.setDescription(department.getDescription().trim());
            }

            // 使用MyBatis-Plus的updateById方法
            boolean result = this.updateById(department);
            if (result) {
                System.out.println("部门修改成功，ID: " + department.getId());
                return Result.success("修改成功");
            } else {
                return Result.fail("修改失败");
            }
        } catch (Exception e) {
            System.err.println("修改部门异常: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("修改部门失败：" + e.getMessage());
        }
    }

    @Override
    public Result deleteDept(Long id) {
        try {
            // 验证ID
            if (Objects.isNull(id)) {
                return Result.fail("部门ID不能为空");
            }

            // 检查部门是否存在
            Department existingDept = departmentMapper.selectById(id);
            if (Objects.isNull(existingDept)) {
                return Result.fail("部门不存在");
            }

            // 检查该部门下是否有员工（使用Mybatis-Plus的QueryWrapper）
            QueryWrapper<Employee> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("department_id", id);
            long employeeCount = employeeMapper.selectCount(queryWrapper);

            if (employeeCount > 0) {
                return Result.fail("该部门下有员工,无法删除");
            }

            // 使用MyBatis-Plus的removeById方法
            boolean result = this.removeById(id);
            if (result) {
                System.out.println("部门删除成功，ID: " + id);
                return Result.success("删除成功");
            } else {
                return Result.fail("删除失败");
            }
        } catch (Exception e) {
            System.err.println("删除部门异常: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("删除部门失败：" + e.getMessage());
        }
    }

    @Override
    public Result searchDeptByName(String deptName) {
        try {
            if (Objects.isNull(deptName) || deptName.trim().isEmpty()) {
                return Result.fail("搜索关键词不能为空");
            }

            // 使用MyBatis-Plus的条件查询
            QueryWrapper<Department> queryWrapper = new QueryWrapper<>();
            queryWrapper.like("name", deptName.trim());

            List<Department> departments = departmentMapper.selectList(queryWrapper);

            System.out.println("搜索到部门数量: " + departments.size());
            return Result.success(departments.size(), departments);
        } catch (Exception e) {
            System.err.println("搜索部门异常: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("搜索部门失败：" + e.getMessage());
        }
    }

    @Override
    public Result getAllDepts() {
        try {
            // 构建查询条件
            QueryWrapper<Department> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByAsc("id"); // 按ID升序排列

            // 执行查询 - 使用ServiceImpl的list方法
            List<Department> departments = this.list(queryWrapper);

            System.out.println("获取所有部门成功，数量: " + departments.size());
            return Result.success("获取部门列表成功", departments);

        } catch (Exception e) {
            System.err.println("获取所有部门失败: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("获取部门列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result initDepartments() {
        try {
            // 检查是否已有部门数据
            long count = this.count();
            if (count > 0) {
                System.out.println("部门数据已存在，数量: " + count);
                return Result.success("部门数据已存在，数量: " + count);
            }

            // 创建初始部门数据
            List<Department> departments = new ArrayList<>();

            Department dept1 = new Department();
            dept1.setName("技术部");
            dept1.setDescription("负责技术开发和维护");
            departments.add(dept1);

            Department dept2 = new Department();
            dept2.setName("销售部");
            dept2.setDescription("负责产品销售和客户关系");
            departments.add(dept2);

            Department dept3 = new Department();
            dept3.setName("人事部");
            dept3.setDescription("负责人力资源管理");
            departments.add(dept3);

            Department dept4 = new Department();
            dept4.setName("财务部");
            dept4.setDescription("负责财务管理和会计");
            departments.add(dept4);

            Department dept5 = new Department();
            dept5.setName("市场部");
            dept5.setDescription("负责市场推广和品牌建设");
            departments.add(dept5);

            // 批量保存
            boolean result = this.saveBatch(departments);
            if (result) {
                System.out.println("初始化部门数据成功，创建了 " + departments.size() + " 个部门");
                return Result.success("初始化部门数据成功，创建了 " + departments.size() + " 个部门");
            } else {
                return Result.fail("初始化部门数据失败");
            }

        } catch (Exception e) {
            System.err.println("初始化部门数据失败: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("初始化部门数据失败: " + e.getMessage());
        }
    }

}
