package qidian.it.springboot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import qidian.it.springboot.entity.Product;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.mapper.ProductMapper;
import qidian.it.springboot.service.ProductService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 商品服务实现类
 */
@Service
public class ProductServiceImpl implements ProductService {
    
    @Resource
    private ProductMapper productMapper;
    
    @Override
    public Result getAllProducts(Integer currentPage) {
        try {
            // 设置分页参数
            Page<Product> page = new Page<>(currentPage, 10); // 每页10条
            
            // 使用Mybatis-Plus分页查询
            Page<Product> productPage = productMapper.selectPage(page, null);
            
            return Result.success("查询成功", productPage);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail("查询失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result getProductById(Integer productId) {
        try {
            Product product = productMapper.selectById(productId);
            if (Objects.nonNull(product)) {
                return Result.success("查询成功", product);
            } else {
                return Result.fail("商品不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail("查询失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result addProduct(Product product) {
        try {
            // 验证必填字段
            if (Objects.isNull(product.getProductName()) || product.getProductName().trim().isEmpty()) {
                return Result.fail("商品名称不能为空");
            }
            if (Objects.isNull(product.getPrice()) || product.getPrice() <= 0) {
                return Result.fail("商品价格必须大于0");
            }
            if (Objects.isNull(product.getStorageNum()) || product.getStorageNum() < 0) {
                return Result.fail("库存数量不能为负数");
            }

            // 处理商品名称和描述的空格
            product.setProductName(product.getProductName().trim());
            if (product.getDescription() != null) {
                product.setDescription(product.getDescription().trim());
            }

            // 记录日志
            System.out.println("准备添加商品:");
            System.out.println("商品名称: " + product.getProductName());
            System.out.println("商品价格: " + product.getPrice());
            System.out.println("库存数量: " + product.getStorageNum());
            System.out.println("商品描述: " + product.getDescription());
            System.out.println("商品图片: " + product.getProductImages());

            int result = productMapper.insert(product);
            if (result > 0) {
                System.out.println("商品添加成功，ID: " + product.getProductId());
                return Result.success("添加商品成功");
            } else {
                System.err.println("商品添加失败，数据库插入返回0");
                return Result.fail("添加商品失败");
            }
        } catch (Exception e) {
            System.err.println("添加商品异常: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("添加商品失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result updateProduct(Product product) {
        try {
            // 验证商品是否存在
            Product existProduct = productMapper.selectById(product.getProductId());
            if (Objects.isNull(existProduct)) {
                return Result.fail("商品不存在");
            }
            
            // 验证必填字段
            if (Objects.nonNull(product.getProductName()) && product.getProductName().trim().isEmpty()) {
                return Result.fail("商品名称不能为空");
            }
            if (Objects.nonNull(product.getPrice()) && product.getPrice() <= 0) {
                return Result.fail("商品价格必须大于0");
            }
            if (Objects.nonNull(product.getStorageNum()) && product.getStorageNum() < 0) {
                return Result.fail("库存数量不能为负数");
            }
            
            int result = productMapper.updateById(product);
            if (result > 0) {
                return Result.success("更新商品成功");
            } else {
                return Result.fail("更新商品失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail("更新商品失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result deleteProduct(Integer productId) {
        try {
            // 验证商品是否存在
            Product product = productMapper.selectById(productId);
            if (Objects.isNull(product)) {
                return Result.fail("商品不存在");
            }
            
            int result = productMapper.deleteById(productId);
            if (result > 0) {
                return Result.success("删除商品成功");
            } else {
                return Result.fail("删除商品失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail("删除商品失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result searchByProductName(String productName) {
        try {
            List<Product> products = productMapper.selectByProductNameLike(productName);
            return Result.success("查询成功", products);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail("查询失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result searchByPriceRange(Double minPrice, Double maxPrice) {
        try {
            if (minPrice > maxPrice) {
                return Result.fail("最低价格不能大于最高价格");
            }
            List<Product> products = productMapper.selectByPriceRange(minPrice, maxPrice);
            return Result.success("查询成功", products);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail("查询失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result getLowStockProducts(Integer threshold) {
        try {
            List<Product> products = productMapper.selectLowStockProducts(threshold);
            return Result.success("查询成功", products);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail("查询失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result updateStock(Integer productId, Integer quantity) {
        try {
            // 验证商品是否存在
            Product product = productMapper.selectById(productId);
            if (Objects.isNull(product)) {
                return Result.fail("商品不存在");
            }
            
            if (quantity < 0) {
                return Result.fail("库存数量不能为负数");
            }
            
            // 更新库存
            UpdateWrapper<Product> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("product_id", productId);
            updateWrapper.set("storage_num", quantity);
            
            int result = productMapper.update(null, updateWrapper);
            if (result > 0) {
                return Result.success("更新库存成功");
            } else {
                return Result.fail("更新库存失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail("更新库存失败：" + e.getMessage());
        }
    }
}
