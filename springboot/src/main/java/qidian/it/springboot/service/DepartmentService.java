package qidian.it.springboot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import qidian.it.springboot.entity.Department;
import qidian.it.springboot.entity.Result;

public interface DepartmentService extends IService<Department> {
    // MyBatis-Plus的IService已经提供了基本的CRUD操作

    /**
     * 添加部门
     * @param department 部门信息
     * @return 操作结果
     */
    Result addDept(Department department);

    /**
     * 获取部门信息（分页）
     * @param currentPage 当前页码
     * @return 操作结果
     */
    Result getDeptInfo(Integer currentPage);

    /**
     * 更新部门信息
     * @param department 部门信息
     * @return 操作结果
     */
    Result updateDept(Department department);

    /**
     * 删除部门
     * @param id 部门ID
     * @return 操作结果
     */
    Result deleteDept(Long id);

    /**
     * 根据部门名称搜索部门
     * @param deptName 部门名称
     * @return 操作结果
     */
    Result searchDeptByName(String deptName);

    /**
     * 获取所有部门列表（不分页，用于下拉选择）
     * @return 操作结果
     */
    Result getAllDepts();

    /**
     * 初始化部门数据
     * @return 操作结果
     */
    Result initDepartments();

}
