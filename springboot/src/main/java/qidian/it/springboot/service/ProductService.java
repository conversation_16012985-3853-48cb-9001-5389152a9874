package qidian.it.springboot.service;

import qidian.it.springboot.entity.Product;
import qidian.it.springboot.entity.Result;

/**
 * 商品服务接口
 */
public interface ProductService {
    
    /**
     * 获取所有商品信息（分页）
     * @param currentPage 当前页码
     * @return 分页结果
     */
    Result getAllProducts(Integer currentPage);
    
    /**
     * 根据商品ID获取商品详情
     * @param productId 商品ID
     * @return 商品信息
     */
    Result getProductById(Integer productId);
    
    /**
     * 添加商品
     * @param product 商品信息
     * @return 操作结果
     */
    Result addProduct(Product product);
    
    /**
     * 更新商品信息
     * @param product 商品信息
     * @return 操作结果
     */
    Result updateProduct(Product product);
    
    /**
     * 删除商品
     * @param productId 商品ID
     * @return 操作结果
     */
    Result deleteProduct(Integer productId);
    
    /**
     * 根据商品名称模糊查询
     * @param productName 商品名称
     * @return 查询结果
     */
    Result searchByProductName(String productName);
    
    /**
     * 根据价格区间查询商品
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @return 查询结果
     */
    Result searchByPriceRange(Double minPrice, Double maxPrice);
    
    /**
     * 查询库存不足的商品
     * @param threshold 库存阈值
     * @return 查询结果
     */
    Result getLowStockProducts(Integer threshold);
    
    /**
     * 更新商品库存
     * @param productId 商品ID
     * @param quantity 库存数量
     * @return 操作结果
     */
    Result updateStock(Integer productId, Integer quantity);
}
