package qidian.it.springboot.service;

import qidian.it.springboot.entity.Result;
import qidian.it.springboot.entity.Admin;

public interface AdminService {

    //登录功能
    Result login(String username, String password);

    //注册功能
    Result register(String username, String password, String email);

    //获取管理员信息（分页）
    Result getAdminInfo(Integer currentPage);

    //更新管理员信息
    Result updateAdmin(Admin admin);

    //根据用户名模糊查询
    Result selectLikeUsername(String username);

    //删除管理员
    Result deleteAdmin(Long id);
}
