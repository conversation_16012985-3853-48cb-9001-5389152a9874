package qidian.it.springboot.service.impl;

import org.springframework.stereotype.Service;
import qidian.it.springboot.config.JwtConfig;
import qidian.it.springboot.entity.Admin;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.entity.User;
import qidian.it.springboot.mapper.AdminMapper;
import qidian.it.springboot.service.AdminService;
import qidian.it.springboot.util.MD5Util;
import qidian.it.springboot.util.RedisUtil;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
public class AdminServiceImpl implements AdminService {

    @Resource
    AdminMapper adminMapper;

    @Resource
    RedisUtil redisUtil;

    @Resource
    JwtConfig jwtConfig;

    @Override
    public Result login(String username, String password) {
        Admin admin = adminMapper.selectByUsername(username);
        if(Objects.nonNull(admin)) { // 用户名正确,继续判断密码是否正确
            if(MD5Util.checkPassword(password, admin.getPassword())) { // 密码正确

                // 生成JWT token
                String token = jwtConfig.createToken(username);

                // 1.将用户信息存入redis（保留原有逻辑，可选）
                redisUtil.set(username, admin); // key是用户名,value是用户对象
                // 2.设置key的有效时间
                redisUtil.expire(username, (int) jwtConfig.getExpire());

                // 返回token和用户信息
                Map<String, Object> data = new HashMap<>();
                data.put("token", token);
                data.put("userInfo", admin);
                data.put("expire", jwtConfig.getExpire());

                System.out.println("用户登录成功: " + username + ", token: " + token);
                return Result.success("登录成功", data);
            } else {
                return Result.fail("密码错误");
            }
        }
        return Result.fail("用户名不存在");
    }


    @Override
    public Result register(String username, String password) {
        Admin admin= adminMapper.selectByUsername(username);
        if(Objects.isNull(admin)){//用户名不存在,允许注册
            admin=new Admin();
            admin.setUsername(username);
            admin.setPassword(MD5Util.MD5PassWord(password));//密码加密
            // 使用Mybatis-Plus的insert方法
            if(adminMapper.insert(admin)>0){//数据添加成功
                return Result.success("注册成功");
            }else{
                return Result.fail("服务器繁忙,请稍候");
            }
        }
        return Result.fail("用户名已存在");
    }
}
