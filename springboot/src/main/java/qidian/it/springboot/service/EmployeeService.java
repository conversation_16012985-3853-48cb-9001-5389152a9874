package qidian.it.springboot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import qidian.it.springboot.entity.Employee;
import qidian.it.springboot.entity.Result;

import java.util.List;

/**
 * 员工服务接口 - 重新设计
 */
public interface EmployeeService extends IService<Employee> {

    /**
     * 分页查询员工列表
     * @param currentPage 当前页
     * @param pageSize 每页大小
     * @param name 员工姓名（模糊查询）
     * @param gender 性别
     * @param departmentId 部门ID
     * @return 分页结果
     */
    Result getEmployeePage(Integer currentPage, Integer pageSize, String name, String gender, Long departmentId);

    /**
     * 添加员工
     * @param employee 员工信息
     * @return 操作结果
     */
    Result addEmployee(Employee employee);

    /**
     * 更新员工信息
     * @param employee 员工信息
     * @return 操作结果
     */
    Result updateEmployee(Employee employee);

    /**
     * 根据ID获取员工信息
     * @param id 员工ID
     * @return 员工信息
     */
    Result getEmployeeById(Long id);

    /**
     * 删除员工
     * @param id 员工ID
     * @return 操作结果
     */
    Result deleteEmployee(Long id);

    /**
     * 根据姓名模糊查询员工
     * @param name 员工姓名
     * @return 员工列表
     */
    Result searchEmployeesByName(String name);

    // ========== 兼容性方法 ==========
    Result addEmp(Employee employee);
    Result getUserInfo(Integer currentPage);
    Result updateUser(Employee employee);
    Result selectLikeUsername(String username);
    Result deleteEmp(Long id);
}
