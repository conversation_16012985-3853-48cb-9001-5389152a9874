package qidian.it.springboot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import qidian.it.springboot.entity.Employee;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.mapper.EmployeeMapper;
import qidian.it.springboot.service.EmployeeService;

import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 员工服务实现类 - 重新设计
 */
@Service
public class EmployeeServiceImpl extends ServiceImpl<EmployeeMapper, Employee> implements EmployeeService {

    @Autowired
    private EmployeeMapper employeeMapper;

    // 手机号正则表达式
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    // 邮箱正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

    @Override
    public Result getEmployeePage(Integer currentPage, Integer pageSize, String name, String gender, Long departmentId) {
        try {
            System.out.println("=== 分页查询员工开始 ===");
            System.out.println("查询参数 - 当前页: " + currentPage + ", 每页: " + pageSize +
                             ", 姓名: " + name + ", 性别: " + gender + ", 部门ID: " + departmentId);

            // 参数验证
            if (currentPage == null || currentPage < 1) {
                currentPage = 1;
            }
            if (pageSize == null || pageSize < 1 || pageSize > 100) {
                pageSize = 10;
            }

            // 创建分页对象
            Page<Employee> page = new Page<>(currentPage, pageSize);

            // 构建查询条件
            QueryWrapper<Employee> queryWrapper = new QueryWrapper<>();

            // 姓名模糊查询
            if (StringUtils.hasText(name)) {
                queryWrapper.like("name", name.trim());
            }

            // 性别精确查询
            if (StringUtils.hasText(gender)) {
                queryWrapper.eq("gender", gender.trim());
            }

            // 部门ID查询
            if (departmentId != null && departmentId > 0) {
                queryWrapper.eq("department_id", departmentId);
            }

            // 按ID降序排列，最新的员工在前面
            queryWrapper.orderByDesc("id");

            // 执行分页查询
            IPage<Employee> pageResult = this.page(page, queryWrapper);

            System.out.println("查询结果 - 总数: " + pageResult.getTotal() + ", 当前页记录数: " + pageResult.getRecords().size());
            System.out.println("=== 分页查询员工结束 ===");

            Result result = Result.success("查询成功", pageResult.getRecords());
            result.setTotal((int) pageResult.getTotal());
            return result;

        } catch (Exception e) {
            System.err.println("分页查询员工失败: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("查询员工列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result addEmployee(Employee employee) {
        try {
            System.out.println("=== 添加员工业务逻辑开始 ===");
            System.out.println("接收到的员工信息: " + employee);

            // 数据验证
            Result validationResult = validateEmployeeData(employee, false);
            if (validationResult != null) {
                return validationResult;
            }

            // 检查手机号是否已存在
            QueryWrapper<Employee> phoneQuery = new QueryWrapper<>();
            phoneQuery.eq("phone", employee.getPhone().trim());
            if (this.count(phoneQuery) > 0) {
                System.out.println("手机号已存在: " + employee.getPhone());
                return Result.fail("手机号码已存在，请使用其他手机号");
            }

            // 检查邮箱是否已存在
            QueryWrapper<Employee> emailQuery = new QueryWrapper<>();
            emailQuery.eq("email", employee.getEmail().trim().toLowerCase());
            if (this.count(emailQuery) > 0) {
                System.out.println("邮箱已存在: " + employee.getEmail());
                return Result.fail("邮箱已存在，请使用其他邮箱");
            }

            // 数据预处理
            preprocessEmployeeData(employee, true);

            // 保存员工
            boolean result = this.save(employee);
            if (result) {
                System.out.println("员工添加成功 - ID: " + employee.getId() + ", 姓名: " + employee.getName());
                System.out.println("=== 添加员工业务逻辑结束 ===");
                return Result.success("员工添加成功", employee);
            } else {
                System.err.println("员工添加失败 - 数据库保存失败");
                return Result.fail("员工添加失败，请重试");
            }

        } catch (Exception e) {
            System.err.println("添加员工异常: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("添加员工失败: " + e.getMessage());
        }
    }

    @Override
    public Result updateEmployee(Employee employee) {
        try {
            System.out.println("=== 更新员工业务逻辑开始 ===");
            System.out.println("接收到的员工信息: " + employee);

            // 验证员工ID
            if (employee.getId() == null || employee.getId() <= 0) {
                System.out.println("员工ID无效: " + employee.getId());
                return Result.fail("员工ID不能为空或无效");
            }

            // 检查员工是否存在
            Employee existEmployee = this.getById(employee.getId());
            if (existEmployee == null) {
                System.out.println("员工不存在，ID: " + employee.getId());
                return Result.fail("员工不存在，无法更新");
            }

            System.out.println("原有员工信息: " + existEmployee);

            // 数据验证
            Result validationResult = validateEmployeeData(employee, true);
            if (validationResult != null) {
                return validationResult;
            }

            // 检查手机号是否被其他员工使用
            if (StringUtils.hasText(employee.getPhone()) && !employee.getPhone().trim().equals(existEmployee.getPhone())) {
                QueryWrapper<Employee> phoneQuery = new QueryWrapper<>();
                phoneQuery.eq("phone", employee.getPhone().trim());
                phoneQuery.ne("id", employee.getId());
                if (this.count(phoneQuery) > 0) {
                    System.out.println("手机号已被其他员工使用: " + employee.getPhone());
                    return Result.fail("手机号码已被其他员工使用，请使用其他手机号");
                }
            }

            // 检查邮箱是否被其他员工使用
            if (StringUtils.hasText(employee.getEmail()) && !employee.getEmail().trim().toLowerCase().equals(existEmployee.getEmail())) {
                QueryWrapper<Employee> emailQuery = new QueryWrapper<>();
                emailQuery.eq("email", employee.getEmail().trim().toLowerCase());
                emailQuery.ne("id", employee.getId());
                if (this.count(emailQuery) > 0) {
                    System.out.println("邮箱已被其他员工使用: " + employee.getEmail());
                    return Result.fail("邮箱已被其他员工使用，请使用其他邮箱");
                }
            }

            // 数据预处理
            preprocessEmployeeData(employee, false);

            // 保留原有的入职日期（如果前端没有传入）
            if (employee.getHireDate() == null) {
                employee.setHireDate(existEmployee.getHireDate());
            }

            // 更新员工信息
            boolean result = this.updateById(employee);
            if (result) {
                System.out.println("员工更新成功 - ID: " + employee.getId() + ", 姓名: " + employee.getName());
                System.out.println("=== 更新员工业务逻辑结束 ===");
                return Result.success("员工信息更新成功", employee);
            } else {
                System.err.println("员工更新失败 - 数据库更新失败");
                return Result.fail("员工信息更新失败，请重试");
            }

        } catch (Exception e) {
            System.err.println("更新员工异常: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("更新员工失败: " + e.getMessage());
        }
    }

    /**
     * 验证员工数据
     * @param employee 员工信息
     * @param isUpdate 是否为更新操作
     * @return 验证结果，null表示验证通过
     */
    private Result validateEmployeeData(Employee employee, boolean isUpdate) {
        System.out.println("=== 开始验证员工数据 ===");
        System.out.println("员工信息详细: " + employee);
        System.out.println("姓名: '" + employee.getName() + "'");
        System.out.println("性别: '" + employee.getGender() + "'");
        System.out.println("年龄: " + employee.getAge());
        System.out.println("手机号: '" + employee.getPhone() + "'");
        System.out.println("邮箱: '" + employee.getEmail() + "'");
        System.out.println("部门ID: " + employee.getDepartmentId());
        System.out.println("职位: '" + employee.getPosition() + "'");
        System.out.println("状态: " + employee.getStatus());

        // 验证姓名
        if (!StringUtils.hasText(employee.getName())) {
            System.out.println("验证失败: 员工姓名为空");
            return Result.fail("员工姓名不能为空");
        }
        if (employee.getName().trim().length() > 50) {
            System.out.println("验证失败: 员工姓名长度超过50个字符");
            return Result.fail("员工姓名长度不能超过50个字符");
        }

        // 验证性别 - 改为必填
        if (!StringUtils.hasText(employee.getGender())) {
            System.out.println("验证失败: 性别为空");
            return Result.fail("请选择员工性别");
        }
        String gender = employee.getGender().trim();
        if (!"男".equals(gender) && !"女".equals(gender)) {
            System.out.println("验证失败: 性别值不正确 - " + gender);
            return Result.fail("性别只能是'男'或'女'");
        }

        // 验证年龄
        if (employee.getAge() != null) {
            if (employee.getAge() < 16 || employee.getAge() > 70) {
                System.out.println("验证失败: 年龄不在有效范围 - " + employee.getAge());
                return Result.fail("年龄必须在16-70岁之间");
            }
        }

        // 验证手机号
        if (!StringUtils.hasText(employee.getPhone())) {
            System.out.println("验证失败: 手机号为空");
            return Result.fail("手机号码不能为空");
        }
        if (!PHONE_PATTERN.matcher(employee.getPhone().trim()).matches()) {
            System.out.println("验证失败: 手机号格式不正确 - " + employee.getPhone());
            return Result.fail("手机号码格式不正确，请输入11位有效手机号");
        }

        // 验证邮箱
        if (!StringUtils.hasText(employee.getEmail())) {
            System.out.println("验证失败: 邮箱为空");
            return Result.fail("邮箱不能为空");
        }
        if (!EMAIL_PATTERN.matcher(employee.getEmail().trim()).matches()) {
            System.out.println("验证失败: 邮箱格式不正确 - " + employee.getEmail());
            return Result.fail("邮箱格式不正确，请输入有效邮箱地址");
        }

        // 验证部门ID
        if (employee.getDepartmentId() == null || employee.getDepartmentId() <= 0) {
            System.out.println("验证失败: 部门ID无效 - " + employee.getDepartmentId());
            return Result.fail("请选择员工所属部门");
        }

        // 验证职位
        if (StringUtils.hasText(employee.getPosition()) && employee.getPosition().trim().length() > 50) {
            System.out.println("验证失败: 职位名称长度超过50个字符");
            return Result.fail("职位名称长度不能超过50个字符");
        }

        System.out.println("=== 员工数据验证通过 ===");
        return null; // 验证通过
    }

    /**
     * 预处理员工数据
     * @param employee 员工信息
     * @param isAdd 是否为添加操作
     */
    private void preprocessEmployeeData(Employee employee, boolean isAdd) {
        // 处理字符串字段，去除首尾空格
        if (StringUtils.hasText(employee.getName())) {
            employee.setName(employee.getName().trim());
        }
        if (StringUtils.hasText(employee.getGender())) {
            employee.setGender(employee.getGender().trim());
        }
        if (StringUtils.hasText(employee.getPhone())) {
            employee.setPhone(employee.getPhone().trim());
        }
        if (StringUtils.hasText(employee.getEmail())) {
            employee.setEmail(employee.getEmail().trim().toLowerCase());
        }
        if (StringUtils.hasText(employee.getPosition())) {
            employee.setPosition(employee.getPosition().trim());
        }

        // 设置默认值
        if (employee.getStatus() == null) {
            employee.setStatus((byte) 1); // 默认启用
        }

        // 添加操作时设置入职日期
        if (isAdd && employee.getHireDate() == null) {
            employee.setHireDate(new Date());
        }
    }

    @Override
    public Result getEmployeeById(Long id) {
        try {
            System.out.println("=== 获取员工信息开始 ===");
            System.out.println("员工ID: " + id);

            if (id == null || id <= 0) {
                return Result.fail("员工ID不能为空或无效");
            }

            Employee employee = this.getById(id);
            if (employee == null) {
                System.out.println("员工不存在，ID: " + id);
                return Result.fail("员工不存在");
            }

            System.out.println("获取员工信息成功: " + employee);
            System.out.println("=== 获取员工信息结束 ===");

            return Result.success("获取成功", employee);

        } catch (Exception e) {
            System.err.println("获取员工信息失败: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("获取员工信息失败: " + e.getMessage());
        }
    }

    @Override
    public Result deleteEmployee(Long id) {
        try {
            System.out.println("=== 删除员工业务逻辑开始 ===");
            System.out.println("员工ID: " + id);

            // 验证员工ID
            if (id == null || id <= 0) {
                return Result.fail("员工ID不能为空或无效");
            }

            // 检查员工是否存在
            Employee employee = this.getById(id);
            if (employee == null) {
                System.out.println("员工不存在，ID: " + id);
                return Result.fail("员工不存在，无法删除");
            }

            System.out.println("准备删除员工: " + employee.getName());

            // 删除员工
            boolean result = this.removeById(id);
            if (result) {
                System.out.println("员工删除成功 - ID: " + id + ", 姓名: " + employee.getName());
                System.out.println("=== 删除员工业务逻辑结束 ===");
                return Result.success("员工删除成功");
            } else {
                System.err.println("员工删除失败 - 数据库删除失败");
                return Result.fail("员工删除失败，请重试");
            }

        } catch (Exception e) {
            System.err.println("删除员工异常: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("删除员工失败: " + e.getMessage());
        }
    }

    // ========== 兼容性方法 ==========

    @Override
    public Result searchEmployeesByName(String name) {
        try {
            System.out.println("=== 搜索员工开始 ===");
            System.out.println("搜索关键词: " + name);

            // 验证搜索关键词
            if (!StringUtils.hasText(name)) {
                return Result.fail("搜索关键词不能为空");
            }

            String keyword = name.trim();
            if (keyword.length() > 50) {
                return Result.fail("搜索关键词长度不能超过50个字符");
            }

            // 模糊查询
            QueryWrapper<Employee> queryWrapper = new QueryWrapper<>();
            queryWrapper.like("name", keyword);
            queryWrapper.orderByDesc("id");

            // 限制搜索结果数量，避免返回过多数据
            queryWrapper.last("LIMIT 100");

            List<Employee> employees = this.list(queryWrapper);

            System.out.println("搜索员工成功，关键词: " + keyword + ", 结果数量: " + employees.size());
            System.out.println("=== 搜索员工结束 ===");

            Result result = Result.success("搜索成功", employees);
            result.setTotal(employees.size());
            return result;

        } catch (Exception e) {
            System.err.println("搜索员工失败: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("搜索员工失败: " + e.getMessage());
        }
    }

    // ========== 兼容性方法 ==========
    @Override
    public Result addEmp(Employee employee) {
        return addEmployee(employee);
    }


    @Override
    public Result getUserInfo(Integer currentPage) {
        // 使用新的分页方法，默认每页10条，兼容原有接口
        return getEmployeePage(currentPage, 10, null, null, null);
    }

    @Override
    public Result updateUser(Employee employee) {
        return updateEmployee(employee);
    }

    @Override
    public Result selectLikeUsername(String username) {
        return searchEmployeesByName(username);
    }

    @Override
    public Result deleteEmp(Long id) {
        return deleteEmployee(id);
    }

}
