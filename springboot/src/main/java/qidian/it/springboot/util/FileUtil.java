package qidian.it.springboot.util;

import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.UUID;

/**
 * 文件上传工具类
 */
public class FileUtil {
    
    // Windows环境下的上传目录
    private static final String UPLOAD_DIR = "D:\\product_image";
    
    // 服务器访问路径前缀
    private static final String SERVER_URL = "http://localhost:8082";
    
    /**
     * 上传文件
     * @param file 上传的文件
     * @return 文件访问URL
     */
    public static String upload(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("上传文件不能为空");
        }
        
        // 获取原始文件名
        String originalFileName = file.getOriginalFilename();
        if (originalFileName == null) {
            throw new RuntimeException("文件名不能为空");
        }
        
        // 验证文件类型
        if (!isValidImageFile(originalFileName)) {
            throw new RuntimeException("只支持jpg、jpeg、png、gif格式的图片文件");
        }
        
        // 确保上传目录存在
        File uploadDirFile = new File(UPLOAD_DIR);
        if (!uploadDirFile.exists()) {
            boolean created = uploadDirFile.mkdirs();
            if (!created) {
                throw new RuntimeException("创建上传目录失败: " + UPLOAD_DIR);
            }
            System.out.println("创建上传目录: " + UPLOAD_DIR);
        }
        
        // 生成新的文件名（避免重复）
        String newFileName = generateFileName(originalFileName);
        
        // 创建目标文件
        File destFile = new File(uploadDirFile, newFileName);
        
        try {
            // 保存文件
            file.transferTo(destFile);
            System.out.println("文件上传成功: " + destFile.getAbsolutePath());
            
            // 返回文件访问URL
            return SERVER_URL + "/product_image/" + newFileName;
            
        } catch (IOException e) {
            System.err.println("文件上传失败: " + e.getMessage());
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证是否为有效的图片文件
     * @param fileName 文件名
     * @return 是否为有效图片
     */
    private static boolean isValidImageFile(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        
        String lowerCaseFileName = fileName.toLowerCase();
        return lowerCaseFileName.endsWith(".jpg") ||
               lowerCaseFileName.endsWith(".jpeg") ||
               lowerCaseFileName.endsWith(".png") ||
               lowerCaseFileName.endsWith(".gif");
    }
    
    /**
     * 生成新的文件名
     * @param originalFileName 原始文件名
     * @return 新的文件名
     */
    private static String generateFileName(String originalFileName) {
        // 获取文件扩展名
        String extension = "";
        int lastDotIndex = originalFileName.lastIndexOf(".");
        if (lastDotIndex > 0) {
            extension = originalFileName.substring(lastDotIndex);
        }
        
        // 生成时间戳
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String timestamp = sdf.format(new Date());
        
        // 生成随机数
        int randomNum = new Random().nextInt(9999) + 1000;
        
        // 生成UUID的前8位
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        
        // 组合新文件名: 时间戳_随机数_UUID_原始名称
        return timestamp + "_" + randomNum + "_" + uuid + "_" + 
               originalFileName.replaceAll("[^a-zA-Z0-9.]", "_") + extension;
    }
    
    /**
     * 删除文件
     * @param fileUrl 文件URL
     * @return 是否删除成功
     */
    public static boolean deleteFile(String fileUrl) {
        if (fileUrl == null || !fileUrl.startsWith(SERVER_URL)) {
            return false;
        }
        
        try {
            // 从URL中提取文件名
            String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
            File file = new File(UPLOAD_DIR, fileName);
            
            if (file.exists()) {
                boolean deleted = file.delete();
                if (deleted) {
                    System.out.println("文件删除成功: " + file.getAbsolutePath());
                } else {
                    System.err.println("文件删除失败: " + file.getAbsolutePath());
                }
                return deleted;
            }
        } catch (Exception e) {
            System.err.println("删除文件时发生错误: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * 获取文件大小的可读格式
     * @param size 文件大小（字节）
     * @return 可读的文件大小
     */
    public static String getReadableFileSize(long size) {
        if (size <= 0) return "0 B";
        
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        return String.format("%.1f %s", 
                size / Math.pow(1024, digitGroups), 
                units[digitGroups]);
    }
}
