package qidian.it.springboot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import qidian.it.springboot.annotation.RequiredToken;
import qidian.it.springboot.entity.Product;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.service.impl.ProductServiceImpl;
import qidian.it.springboot.util.FileUtil;

/**
 * 商品管理控制器
 */
@RestController
@CrossOrigin
public class ProductController {
    
    @Autowired
    private ProductServiceImpl productService;
    
    /**
     * 获取所有商品信息（分页）
     * @param currentPage 当前页码
     * @return 分页结果
     */
    @RequestMapping("/getAllProducts")
    @RequiredToken
    public Result getAllProducts(Integer currentPage) {
        if (currentPage == null || currentPage <= 0) {
            currentPage = 1;
        }
        return productService.getAllProducts(currentPage);
    }
    
    /**
     * 根据商品ID获取商品详情
     * @param productId 商品ID
     * @return 商品信息
     */
    @RequestMapping("/getProductById")
    @RequiredToken
    public Result getProductById(Integer productId) {
        if (productId == null || productId <= 0) {
            return Result.fail("商品ID不能为空");
        }
        return productService.getProductById(productId);
    }
    
    /**
     * 添加商品
     * @param product 商品信息
     * @return 操作结果
     */
    @RequestMapping("/addProduct")
    @RequiredToken
    public Result addProduct(Product product) {
        return productService.addProduct(product);
    }

    /**
     * 添加商品（带文件上传）
     * @param file 商品图片文件
     * @param productName 商品名称
     * @param price 商品价格
     * @param storageNum 库存数量
     * @param description 商品描述
     * @return 操作结果
     */
    @RequestMapping("/product/addProduct")
    @RequiredToken
    public Result addProductWithImage(
            @RequestParam(value = "file", required = false) MultipartFile file,
            @RequestParam("productName") String productName,
            @RequestParam("price") Double price,
            @RequestParam("storageNum") Integer storageNum,
            @RequestParam(value = "description", required = false) String description) {

        try {
            // 创建商品对象
            Product product = new Product();
            product.setProductName(productName);
            product.setPrice(price);
            product.setStorageNum(storageNum);
            product.setDescription(description);

            // 处理图片上传
            if (file != null && !file.isEmpty()) {
                String imageUrl = FileUtil.upload(file);
                product.setProductImages(imageUrl);
                System.out.println("商品图片上传成功: " + imageUrl);
            } else {
                System.out.println("未上传商品图片");
            }

            // 保存商品信息
            Result result = productService.addProduct(product);

            if (result.getCode() == 200) {
                System.out.println("商品添加成功: " + productName);
                return Result.success("商品添加成功", product);
            } else {
                return result;
            }

        } catch (Exception e) {
            System.err.println("添加商品失败: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("添加商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新商品信息
     * @param product 商品信息
     * @return 操作结果
     */
    @RequestMapping("/updateProduct")
    @RequiredToken
    public Result updateProduct(Product product) {
        if (product.getProductId() == null || product.getProductId() <= 0) {
            return Result.fail("商品ID不能为空");
        }
        return productService.updateProduct(product);
    }
    
    /**
     * 删除商品
     * @param productId 商品ID
     * @return 操作结果
     */
    @RequestMapping("/deleteProduct")
    @RequiredToken
    public Result deleteProduct(Integer productId) {
        if (productId == null || productId <= 0) {
            return Result.fail("商品ID不能为空");
        }
        return productService.deleteProduct(productId);
    }
    
    /**
     * 根据商品名称模糊查询
     * @param productName 商品名称
     * @return 查询结果
     */
    @RequestMapping("/searchProductByName")
    @RequiredToken
    public Result searchByProductName(String productName) {
        if (productName == null || productName.trim().isEmpty()) {
            return Result.fail("商品名称不能为空");
        }
        return productService.searchByProductName(productName);
    }
    
    /**
     * 根据价格区间查询商品
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @return 查询结果
     */
    @RequestMapping("/searchProductByPrice")
    @RequiredToken
    public Result searchByPriceRange(Double minPrice, Double maxPrice) {
        if (minPrice == null || maxPrice == null) {
            return Result.fail("价格区间不能为空");
        }
        if (minPrice < 0 || maxPrice < 0) {
            return Result.fail("价格不能为负数");
        }
        return productService.searchByPriceRange(minPrice, maxPrice);
    }
    
    /**
     * 查询库存不足的商品
     * @param threshold 库存阈值，默认为10
     * @return 查询结果
     */
    @RequestMapping("/getLowStockProducts")
    @RequiredToken
    public Result getLowStockProducts(Integer threshold) {
        if (threshold == null || threshold < 0) {
            threshold = 10; // 默认阈值为10
        }
        return productService.getLowStockProducts(threshold);
    }
    
    /**
     * 更新商品库存
     * @param productId 商品ID
     * @param quantity 库存数量
     * @return 操作结果
     */
    @RequestMapping("/updateProductStock")
    @RequiredToken
    public Result updateStock(Integer productId, Integer quantity) {
        if (productId == null || productId <= 0) {
            return Result.fail("商品ID不能为空");
        }
        if (quantity == null || quantity < 0) {
            return Result.fail("库存数量不能为负数");
        }
        return productService.updateStock(productId, quantity);
    }
}
