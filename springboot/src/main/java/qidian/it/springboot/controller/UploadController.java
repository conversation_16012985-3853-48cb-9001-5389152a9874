package qidian.it.springboot.controller;

import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.util.FileUtil;

/**
 * 文件上传测试控制器
 */
@RestController
@CrossOrigin
public class UploadController {
    
    /**
     * 测试文件上传接口
     * @param file 上传的文件
     * @return 上传结果
     */
    @RequestMapping("/upload")
    public Result upload(@RequestParam("file") MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                return Result.fail("请选择要上传的文件");
            }
            
            System.out.println("接收到上传文件:");
            System.out.println("文件名: " + file.getOriginalFilename());
            System.out.println("文件大小: " + FileUtil.getReadableFileSize(file.getSize()));
            System.out.println("文件类型: " + file.getContentType());
            
            // 上传文件
            String fileUrl = FileUtil.upload(file);
            
            return Result.success("文件上传成功", fileUrl);
            
        } catch (Exception e) {
            System.err.println("文件上传失败: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("文件上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量文件上传接口
     * @param files 上传的文件数组
     * @return 上传结果
     */
    @RequestMapping("/uploadMultiple")
    public Result uploadMultiple(@RequestParam("files") MultipartFile[] files) {
        try {
            if (files == null || files.length == 0) {
                return Result.fail("请选择要上传的文件");
            }
            
            StringBuilder result = new StringBuilder();
            int successCount = 0;
            
            for (MultipartFile file : files) {
                if (file != null && !file.isEmpty()) {
                    try {
                        String fileUrl = FileUtil.upload(file);
                        result.append(fileUrl).append(";");
                        successCount++;
                        System.out.println("文件上传成功: " + file.getOriginalFilename());
                    } catch (Exception e) {
                        System.err.println("文件上传失败: " + file.getOriginalFilename() + " - " + e.getMessage());
                    }
                }
            }
            
            if (successCount > 0) {
                return Result.success("成功上传 " + successCount + " 个文件", result.toString());
            } else {
                return Result.fail("所有文件上传失败");
            }
            
        } catch (Exception e) {
            System.err.println("批量文件上传失败: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("批量文件上传失败: " + e.getMessage());
        }
    }
}
