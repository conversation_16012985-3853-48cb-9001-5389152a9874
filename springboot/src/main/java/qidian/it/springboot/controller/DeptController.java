package qidian.it.springboot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qidian.it.springboot.annotation.RequiredToken;
import qidian.it.springboot.entity.Department;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.service.DepartmentService;

/**
 * 部门管理控制器
 */
@RestController
@CrossOrigin
public class DeptController {

    @Autowired
    private DepartmentService departmentService;
    /**
     * 获取部门信息（分页）
     * @param currentPage 当前页码
     * @return 操作结果
     */
    @RequestMapping("/getDeptInfo")
    @RequiredToken
    public Result getDeptInfo(Integer currentPage) {
        return departmentService.getDeptInfo(currentPage);
    }

    /**
     * 更新部门信息
     * @param department 部门信息
     * @return 操作结果
     */
    @RequestMapping("/updateDept")
    @RequiredToken
    public Result updateDept(Department department) {
        return departmentService.updateDept(department);
    }

    /**
     * 添加部门
     * @param department 部门信息
     * @return 操作结果
     */
    @RequestMapping("/addDept")
    @RequiredToken
    public Result addDept(Department department) {
        return departmentService.addDept(department);
    }

    /**
     * 删除部门
     * @param id 部门ID
     * @return 操作结果
     */
    @RequestMapping("/deleteDept")
    @RequiredToken
    public Result deleteDept(Long id) {
        return departmentService.deleteDept(id);
    }

    /**
     * 根据部门名称搜索部门
     * @param deptName 部门名称
     * @return 操作结果
     */
    @RequestMapping("/searchDeptByName")
    @RequiredToken
    public Result searchDeptByName(String deptName) {
        return departmentService.searchDeptByName(deptName);
    }

    /**
     * 获取所有部门列表（不分页，用于下拉选择）
     * 暂时移除Token验证以便调试
     * @return 操作结果
     */
    @RequestMapping("/getAllDepts")
    public Result getAllDepts() {
        System.out.println("收到获取所有部门的请求");
        Result result = departmentService.getAllDepts();
        System.out.println("返回结果: " + result);
        return result;
    }

    /**
     * 测试接口 - 不需要Token验证
     * @return 操作结果
     */
    @RequestMapping("/testGetAllDepts")
    public Result testGetAllDepts() {
        System.out.println("收到测试获取所有部门的请求");
        Result result = departmentService.getAllDepts();
        System.out.println("测试返回结果: " + result);
        return result;
    }

    /**
     * 初始化部门数据 - 不需要Token验证
     * @return 操作结果
     */
    @RequestMapping("/initDepts")
    public Result initDepts() {
        return departmentService.initDepartments();
    }

}
