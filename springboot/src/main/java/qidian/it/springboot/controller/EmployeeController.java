package qidian.it.springboot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import qidian.it.springboot.annotation.RequiredToken;
import qidian.it.springboot.entity.Employee;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.service.EmployeeService;

import java.util.List;

/**
 * 员工管理控制器 - 重新设计
 * 提供完整的员工增删改查功能
 */
@RestController
@CrossOrigin
public class EmployeeController {

    @Autowired
    private EmployeeService employeeService;

    /**
     * 分页查询员工列表
     * @param currentPage 当前页
     * @param pageSize 每页大小
     * @param name 员工姓名（模糊查询）
     * @param gender 性别
     * @param departmentId 部门ID
     * @return 分页结果
     */
    @GetMapping("/employee/page")
    @RequiredToken
    public Result getEmployeePage(@RequestParam(defaultValue = "1") Integer currentPage,
                                 @RequestParam(defaultValue = "10") Integer pageSize,
                                 @RequestParam(required = false) String name,
                                 @RequestParam(required = false) String gender,
                                 @RequestParam(required = false) Long departmentId) {
        return employeeService.getEmployeePage(currentPage, pageSize, name, gender, departmentId);
    }

    /**
     * 添加员工
     * @param employee 员工信息
     * @return 操作结果
     */
    @PostMapping("/employee/add")
    @RequiredToken
    public Result addEmployee(@RequestBody Employee employee) {
        try {
            System.out.println("=== 添加员工请求开始 ===");
            System.out.println("接收到的员工信息: " + employee);

            Result result = employeeService.addEmployee(employee);

            System.out.println("添加员工结果: " + result);
            System.out.println("=== 添加员工请求结束 ===");

            return result;
        } catch (Exception e) {
            System.err.println("添加员工异常: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("添加员工失败: " + e.getMessage());
        }
    }

    /**
     * 更新员工信息
     * @param employee 员工信息
     * @return 操作结果
     */
    @PutMapping("/employee/update")
    @RequiredToken
    public Result updateEmployee(@RequestBody Employee employee) {
        try {
            System.out.println("=== 更新员工请求开始 ===");
            System.out.println("接收到的员工信息: " + employee);

            Result result = employeeService.updateEmployee(employee);

            System.out.println("更新员工结果: " + result);
            System.out.println("=== 更新员工请求结束 ===");

            return result;
        } catch (Exception e) {
            System.err.println("更新员工异常: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("更新员工失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取员工信息
     * @param id 员工ID
     * @return 员工信息
     */
    @GetMapping("/employee/{id}")
    @RequiredToken
    public Result getEmployeeById(@PathVariable Long id) {
        try {
            System.out.println("=== 获取员工信息请求 ===");
            System.out.println("员工ID: " + id);

            Result result = employeeService.getEmployeeById(id);

            System.out.println("获取员工信息结果: " + result);

            return result;
        } catch (Exception e) {
            System.err.println("获取员工信息异常: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("获取员工信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除员工
     * @param id 员工ID
     * @return 操作结果
     */
    @DeleteMapping("/employee/delete/{id}")
    @RequiredToken
    public Result deleteEmployee(@PathVariable Long id) {
        try {
            System.out.println("=== 删除员工请求开始 ===");
            System.out.println("员工ID: " + id);

            Result result = employeeService.deleteEmployee(id);

            System.out.println("删除员工结果: " + result);
            System.out.println("=== 删除员工请求结束 ===");

            return result;
        } catch (Exception e) {
            System.err.println("删除员工异常: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("删除员工失败: " + e.getMessage());
        }
    }

    // ========== 兼容性接口（保留原有前端调用） ==========

    /**
     * 获取用户信息（兼容原有接口）
     * @param currentPage 当前页
     * @return 分页结果
     */
    @RequestMapping("/getUserInfo")
    @RequiredToken
    public Result getUserInfo(@RequestParam(defaultValue = "1") Integer currentPage) {
        return employeeService.getEmployeePage(currentPage, 10, null, null, null);
    }

    /**
     * 添加员工（兼容原有接口）
     * @param employee 员工信息
     * @return 操作结果
     */
    @RequestMapping("/addEmp")
    @RequiredToken
    public Result addEmp(@RequestBody Employee employee) {
        return addEmployee(employee);
    }

    /**
     * 更新员工信息（兼容原有接口）
     * @param employee 员工信息
     * @return 操作结果
     */
    @RequestMapping("/updateEmployee")
    @RequiredToken
    public Result updateEmployeeCompat(@RequestBody Employee employee) {
        return updateEmployee(employee);
    }

    /**
     * 删除员工（兼容原有接口）
     * @param id 员工ID
     * @return 操作结果
     */
    @RequestMapping("/deleteEmp")
    @RequiredToken
    public Result deleteEmp(@RequestParam Long id) {
        return deleteEmployee(id);
    }

    /**
     * 模糊查询用户名（兼容原有接口）
     * @param username 用户名
     * @return 查询结果
     */
    @RequestMapping("/selectLikeUsername")
    @RequiredToken
    public Result selectLikeUsername(@RequestParam String username) {
        return employeeService.searchEmployeesByName(username);
    }

}

