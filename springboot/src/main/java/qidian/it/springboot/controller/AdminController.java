package qidian.it.springboot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qidian.it.springboot.entity.Admin;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.service.impl.AdminServiceImpl;

import javax.annotation.Resource;

@RestController
@CrossOrigin
public class AdminController {

    @Resource
    AdminServiceImpl adminService;

    @PostMapping("/login")
    public Result login(String username, String password) {
        return adminService.login(username, password);
    }

    @PostMapping("/register")
    public Result register(@RequestBody Admin admin) {
        System.out.println("=== 注册请求 ===");
        System.out.println("用户名: " + admin.getUsername());
        System.out.println("密码: " + admin.getPassword());
        System.out.println("邮箱: " + admin.getEmail());

        return adminService.register(admin.getUsername(), admin.getPassword(), admin.getEmail());
    }

    @RequestMapping("/getAdminInfo")
    public Result getAdminInfo(Integer currentPage) {
        return adminService.getAdminInfo(currentPage);
    }

    @RequestMapping("/updateAdmin")
    public Result updateAdmin(Admin admin) {
        return adminService.updateAdmin(admin);
    }

    @RequestMapping("/selectLikeUsername")
    public Result selectLikeUsername(String username) {
        return adminService.selectLikeUsername(username);
    }

    @RequestMapping("/deleteAdmin")
    public Result deleteAdmin(Long id) {
        return adminService.deleteAdmin(id);
    }
}
