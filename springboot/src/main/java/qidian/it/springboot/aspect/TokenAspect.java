package qidian.it.springboot.aspect;

import io.jsonwebtoken.Claims;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import qidian.it.springboot.config.JwtConfig;
import qidian.it.springboot.entity.Result;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

@Aspect
@Component
public class TokenAspect {

    @Autowired
    private JwtConfig jwtConfig;

    @Pointcut("@annotation(qidian.it.springboot.annotation.RequiredToken)")
    public void checkToken() {}

    /**
     * 环绕通知：验证JWT Token
     * 1. 请求进入后端 => 方法上加了@RequiredToken注解
     * 2. 需要验证token，验证通过后执行目标方法，验证不通过：不执行目标方法
     */
    @Around(value = "checkToken()")
    public Object aroundMethod(ProceedingJoinPoint point) {
        // AOP中获取HttpServletRequest
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return Result.fail("无法获取请求上下文");
        }
        
        HttpServletRequest request = attributes.getRequest();
        String methodName = point.getSignature().getName();
        List<Object> args = Arrays.asList(point.getArgs());
        
        System.out.println("=== JWT Token验证开始 ===");
        System.out.println("请求方法: " + methodName);
        System.out.println("请求参数: " + args);

        try {
            // Token 验证
            String token = request.getHeader(jwtConfig.getHeader());
            System.out.println("前端传过来的token: " + token);

            // 检查token是否为空
            if (ObjectUtils.isEmpty(token)) {
                System.out.println("Token验证失败: token不能为空");
                return Result.fail("token不能为空");
            }

            // 验证token有效性
            Claims claims = null;
            try {
                claims = jwtConfig.getTokenClaim(token);
                if (claims == null || jwtConfig.isTokenExpired(token)) {
                    System.out.println("Token验证失败: token已失效");
                    return Result.fail("token已失效，请重新登录");
                }
                
                // 获取用户信息
                String username = jwtConfig.getUsernameFromToken(token);
                System.out.println("Token验证成功，用户: " + username);
                
                // 可以将用户信息存储到请求属性中，供后续使用
                request.setAttribute("currentUser", username);
                
            } catch (Exception e) {
                System.out.println("Token验证失败: " + e.getMessage());
                return Result.fail("token已失效，请重新登录");
            }

            // 执行目标方法
            Object result = point.proceed();
            System.out.println("=== JWT Token验证结束，方法执行成功 ===");
            return result;

        } catch (Throwable e) {
            System.err.println("方法执行异常: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("服务器内部错误");
        }
    }
}
