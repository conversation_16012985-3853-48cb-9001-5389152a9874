<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
        }
        .upload-section h3 {
            color: #666;
            margin-bottom: 15px;
        }
        input[type="file"] {
            margin: 10px 0;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            min-height: 50px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>商品图片上传测试</h1>
        
        <!-- 单文件上传测试 -->
        <div class="upload-section">
            <h3>单文件上传测试</h3>
            <input type="file" id="singleFile" accept="image/*">
            <br>
            <button onclick="uploadSingle()">上传单个文件</button>
            <div id="singleResult" class="result"></div>
        </div>
        
        <!-- 多文件上传测试 -->
        <div class="upload-section">
            <h3>多文件上传测试</h3>
            <input type="file" id="multipleFiles" accept="image/*" multiple>
            <br>
            <button onclick="uploadMultiple()">上传多个文件</button>
            <div id="multipleResult" class="result"></div>
        </div>
        
        <!-- 商品添加测试 -->
        <div class="upload-section">
            <h3>商品添加测试（带图片）</h3>
            <input type="text" id="productName" placeholder="商品名称" style="width: 200px; margin: 5px;">
            <input type="number" id="productPrice" placeholder="商品价格" style="width: 150px; margin: 5px;">
            <input type="number" id="productStock" placeholder="库存数量" style="width: 150px; margin: 5px;">
            <br>
            <textarea id="productDesc" placeholder="商品描述" style="width: 400px; height: 60px; margin: 5px;"></textarea>
            <br>
            <input type="file" id="productImage" accept="image/*">
            <br>
            <button onclick="addProduct()">添加商品</button>
            <div id="productResult" class="result"></div>
        </div>
    </div>

    <script>
        // 单文件上传
        function uploadSingle() {
            const fileInput = document.getElementById('singleFile');
            const resultDiv = document.getElementById('singleResult');
            
            if (!fileInput.files[0]) {
                showResult(resultDiv, '请选择文件', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            showResult(resultDiv, '正在上传...', 'info');
            
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showResult(resultDiv, `上传成功！文件URL: ${data.data}`, 'success');
                } else {
                    showResult(resultDiv, `上传失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                showResult(resultDiv, `上传错误: ${error.message}`, 'error');
            });
        }
        
        // 多文件上传
        function uploadMultiple() {
            const fileInput = document.getElementById('multipleFiles');
            const resultDiv = document.getElementById('multipleResult');
            
            if (!fileInput.files.length) {
                showResult(resultDiv, '请选择文件', 'error');
                return;
            }
            
            const formData = new FormData();
            for (let file of fileInput.files) {
                formData.append('files', file);
            }
            
            showResult(resultDiv, '正在上传...', 'info');
            
            fetch('/uploadMultiple', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showResult(resultDiv, `上传成功！${data.message}`, 'success');
                } else {
                    showResult(resultDiv, `上传失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                showResult(resultDiv, `上传错误: ${error.message}`, 'error');
            });
        }
        
        // 添加商品
        function addProduct() {
            const resultDiv = document.getElementById('productResult');
            const productName = document.getElementById('productName').value;
            const productPrice = document.getElementById('productPrice').value;
            const productStock = document.getElementById('productStock').value;
            const productDesc = document.getElementById('productDesc').value;
            const productImage = document.getElementById('productImage').files[0];
            
            if (!productName || !productPrice || !productStock) {
                showResult(resultDiv, '请填写商品名称、价格和库存', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('productName', productName);
            formData.append('price', productPrice);
            formData.append('storageNum', productStock);
            formData.append('description', productDesc);
            if (productImage) {
                formData.append('file', productImage);
            }
            
            showResult(resultDiv, '正在添加商品...', 'info');
            
            fetch('/product/addProduct', {
                method: 'POST',
                headers: {
                    'token': 'your-jwt-token-here' // 需要替换为实际的token
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showResult(resultDiv, `商品添加成功！`, 'success');
                    // 清空表单
                    document.getElementById('productName').value = '';
                    document.getElementById('productPrice').value = '';
                    document.getElementById('productStock').value = '';
                    document.getElementById('productDesc').value = '';
                    document.getElementById('productImage').value = '';
                } else {
                    showResult(resultDiv, `添加失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                showResult(resultDiv, `添加错误: ${error.message}`, 'error');
            });
        }
        
        // 显示结果
        function showResult(element, message, type) {
            element.className = `result ${type}`;
            element.innerHTML = message;
        }
    </script>
</body>
</html>
