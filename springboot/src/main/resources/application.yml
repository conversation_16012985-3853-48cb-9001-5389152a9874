server:
  port: 8082

spring:
  application:
    name: springboot
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  datasource:
    username: root
    password: 123456
    url: **************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-timeout: 6000
      maximum-pool-size: 5
  redis:
    database: 0
    host: localhost
    port: 6379
    #password: 123456
    timeout: 5000 # 连接超时时间（毫秒）
    lettuce:
      pool:
        max-active: 20 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000ms # 连接池最大阻塞等待时间（使用负值表示没有限制）




mybatis-plus:
  # 指定 mapper.xml 的位置（保留自定义SQL）
  mapper-locations: classpath:mapping/*.xml
  #扫描实体类的位置
  type-aliases-package: qidian.it.springboot.entity
  configuration:
    #默认开启驼峰命名法
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 主键策略：自增
      id-type: auto
      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0

# JWT配置
config:
  jwt:
    # 加密密钥
    secret: abcdefg1234213213123123123123123123123213123123125gdfgdffdgfgdfdggfd231231232142141412312333322121212222222222222111222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222221111111111sadasdasdasdasdasdsadsadasdsadsadasdasdasdasdsad
    # token有效时长（秒）
    expire: 3600
    # header 名称
    header: token


