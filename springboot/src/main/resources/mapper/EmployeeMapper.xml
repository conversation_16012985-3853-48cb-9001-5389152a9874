<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="qidian.it.springboot.mapper.EmployeeMapper">

    <!-- 员工结果映射 -->
    <resultMap id="BaseResultMap" type="qidian.it.springboot.entity.Employee">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="gender" jdbcType="VARCHAR" property="gender"/>
        <result column="age" jdbcType="INTEGER" property="age"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="department_id" jdbcType="BIGINT" property="departmentId"/>
        <result column="position" jdbcType="VARCHAR" property="position"/>
        <result column="hire_date" jdbcType="TIMESTAMP" property="hireDate"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, name, gender, age, phone, email, department_id, position, hire_date, status
    </sql>

    <!-- 分页查询员工列表（自定义SQL，如果需要复杂查询） -->
    <select id="selectEmployeePage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM employee
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="gender != null and gender != ''">
                AND gender = #{gender}
            </if>
            <if test="departmentId != null and departmentId > 0">
                AND department_id = #{departmentId}
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <!-- 根据部门ID查询员工列表 -->
    <select id="selectByDepartmentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM employee
        WHERE department_id = #{departmentId}
        ORDER BY id DESC
    </select>

    <!-- 根据姓名模糊查询员工 -->
    <select id="selectByNameLike" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM employee
        WHERE name LIKE CONCAT('%', #{name}, '%')
        ORDER BY id DESC
    </select>

    <!-- 统计部门员工数量 -->
    <select id="countByDepartmentId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM employee
        WHERE department_id = #{departmentId}
    </select>

    <!-- 批量更新员工状态 -->
    <update id="updateStatusByIds">
        UPDATE employee
        SET status = #{status}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 保留原有的User相关查询（如果需要） -->
    <select id="selectByUsername" resultType="qidian.it.springboot.entity.User">
        SELECT * FROM user WHERE username = #{username}
    </select>

    <select id="selectLikeUsername" resultType="qidian.it.springboot.entity.User">
        SELECT * FROM user WHERE username LIKE CONCAT('%', #{username}, '%')
    </select>

    <select id="getAllInfo" resultType="qidian.it.springboot.entity.User">
        SELECT * FROM user ORDER BY id DESC
    </select>

</mapper>
