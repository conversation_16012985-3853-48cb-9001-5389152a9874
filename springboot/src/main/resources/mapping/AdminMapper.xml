<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="qidian.it.springboot.mapper.AdminMapper" >

  <!-- 自定义查询方法：根据用户名查询 -->
  <select id="selectByUsername" resultType="qidian.it.springboot.entity.Admin">
    select id, username, password, email
    from admin
    where username = #{username}
  </select>

  <!-- 自定义查询方法：获取所有管理员信息 -->
  <select id="getAllInfo" resultType="qidian.it.springboot.entity.Admin">
    select id, username, password, email
    from admin
  </select>

</mapper>