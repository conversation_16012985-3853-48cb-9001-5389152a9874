<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="qidian.it.springboot.mapper.ProductMapper">
  
  <!-- 根据商品名称模糊查询 -->
  <select id="selectByProductNameLike" resultType="qidian.it.springboot.entity.Product">
    select product_id, product_name, price, storage_num, description, product_images
    from product
    where product_name like concat('%', #{productName}, '%')
    order by product_id
  </select>
  
  <!-- 根据价格区间查询商品 -->
  <select id="selectByPriceRange" resultType="qidian.it.springboot.entity.Product">
    select product_id, product_name, price, storage_num, description, product_images
    from product
    where price between #{minPrice} and #{maxPrice}
    order by price
  </select>
  
  <!-- 查询库存不足的商品 -->
  <select id="selectLowStockProducts" resultType="qidian.it.springboot.entity.Product">
    select product_id, product_name, price, storage_num, description, product_images
    from product
    where storage_num &lt; #{threshold}
    order by storage_num
  </select>
  
  <!-- 获取所有商品信息 -->
  <select id="getAllProducts" resultType="qidian.it.springboot.entity.Product">
    select product_id, product_name, price, storage_num, description, product_images
    from product
    order by product_id
  </select>
  
</mapper>
