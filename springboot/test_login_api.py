#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Mybatis-Plus重构后的登录API
"""

import requests
import json

# 服务器地址
BASE_URL = "http://localhost:8082"

def test_login_success():
    """测试登录成功"""
    print("=== 测试登录成功 ===")
    url = f"{BASE_URL}/login"
    params = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 200:
                print("✅ 登录成功测试通过")
                return True
            else:
                print(f"❌ 登录失败: {data.get('message')}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_login_wrong_password():
    """测试错误密码"""
    print("\n=== 测试错误密码 ===")
    url = f"{BASE_URL}/login"
    params = {
        "username": "admin",
        "password": "wrongpassword"
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("code") != 200 and "密码错误" in data.get("message", ""):
                print("✅ 错误密码测试通过")
                return True
            else:
                print(f"❌ 预期密码错误，但得到: {data.get('message')}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_login_wrong_username():
    """测试错误用户名"""
    print("\n=== 测试错误用户名 ===")
    url = f"{BASE_URL}/login"
    params = {
        "username": "nonexistentuser",
        "password": "admin"
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("code") != 200 and "用户名不存在" in data.get("message", ""):
                print("✅ 错误用户名测试通过")
                return True
            else:
                print(f"❌ 预期用户名不存在，但得到: {data.get('message')}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_register():
    """测试注册功能"""
    print("\n=== 测试注册功能 ===")
    url = f"{BASE_URL}/register"
    params = {
        "username": f"testuser_{int(__import__('time').time())}",  # 使用时间戳避免重复
        "password": "testpassword123"
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 200 and "注册成功" in data.get("message", ""):
                print("✅ 注册功能测试通过")
                return True
            else:
                print(f"❌ 注册失败: {data.get('message')}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试Mybatis-Plus重构后的登录API...")
    print("=" * 50)
    
    # 检查服务器是否可达
    try:
        response = requests.get(f"{BASE_URL}/test", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器连接正常")
        else:
            print("⚠️ 服务器响应异常，但继续测试...")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保Spring Boot应用已启动在端口8082")
        return
    
    # 执行测试
    tests = [
        test_login_success,
        test_login_wrong_password,
        test_login_wrong_username,
        test_register
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！Mybatis-Plus重构成功！")
    else:
        print("⚠️ 部分测试失败，请检查实现")

if __name__ == "__main__":
    main()
