# 商品管理模块开发指南

## 📋 模块概述

已成功为项目创建了完整的商品管理模块，包括实体类、Mapper、Service、Controller等所有必要组件，支持商品的增删改查、库存管理、价格查询等功能。

## 🗃️ 数据库表结构

```sql
CREATE TABLE product(
    product_id INT PRIMARY KEY AUTO_INCREMENT,
    product_name VARCHAR(50) NOT NULL,
    price DOUBLE NOT NULL,
    storage_num INT NOT NULL,
    description VARCHAR(100),
    product_images VARCHAR(100)
);
```

## 🏗️ 项目结构

### 1. 实体类 (`Product.java`)
- 使用Mybatis-Plus注解
- 包含完整的getter/setter方法
- 字符串字段自动trim处理
- 重写toString方法

### 2. Mapper接口 (`ProductMapper.java`)
- 继承BaseMapper<Product>
- 提供基础CRUD操作
- 自定义查询方法：
  - 商品名称模糊查询
  - 价格区间查询
  - 库存不足查询

### 3. XML映射文件 (`ProductMapper.xml`)
- 自定义SQL查询实现
- 支持复杂查询条件
- 结果自动映射

### 4. Service层
- **接口**: `ProductService.java`
- **实现**: `ProductServiceImpl.java`
- 完整的业务逻辑处理
- 参数验证和异常处理

### 5. Controller层 (`ProductController.java`)
- RESTful API设计
- JWT Token验证
- 参数校验
- 统一返回格式

## 🚀 API接口说明

### 基础CRUD操作

#### 1. 获取所有商品（分页）
```
GET /getAllProducts?currentPage=1
Headers: token: your-jwt-token
```

#### 2. 根据ID获取商品详情
```
GET /getProductById?productId=1
Headers: token: your-jwt-token
```

#### 3. 添加商品
```
POST /addProduct
Headers: token: your-jwt-token
Body: productName=新商品&price=999.99&storageNum=100&description=商品描述
```

#### 4. 更新商品
```
POST /updateProduct
Headers: token: your-jwt-token
Body: productId=1&productName=更新商品&price=1299.99&storageNum=80
```

#### 5. 删除商品
```
POST /deleteProduct
Headers: token: your-jwt-token
Body: productId=1
```

### 高级查询功能

#### 6. 商品名称模糊查询
```
GET /searchProductByName?productName=iPhone
Headers: token: your-jwt-token
```

#### 7. 价格区间查询
```
GET /searchProductByPrice?minPrice=1000&maxPrice=5000
Headers: token: your-jwt-token
```

#### 8. 库存不足查询
```
GET /getLowStockProducts?threshold=50
Headers: token: your-jwt-token
```

#### 9. 更新库存
```
POST /updateProductStock
Headers: token: your-jwt-token
Body: productId=1&quantity=200
```

## 🔧 功能特性

### 1. 数据验证
- 商品名称非空验证
- 价格必须大于0
- 库存数量不能为负数
- ID有效性验证

### 2. 安全控制
- 所有接口都需要JWT Token验证
- 统一的权限控制
- 防止未授权访问

### 3. 异常处理
- 完善的异常捕获
- 友好的错误提示
- 统一的返回格式

### 4. 分页支持
- 使用Mybatis-Plus分页插件
- 默认每页10条记录
- 支持自定义页码

## 🧪 测试方法

### 1. 单元测试
```bash
# 运行Product相关测试
mvn test -Dtest=ProductMapperTest
```

### 2. API测试
```bash
# 1. 先登录获取token
curl -X GET "http://localhost:8082/login?username=admin&password=admin"

# 2. 使用token访问商品接口
curl -H "token: your-jwt-token" "http://localhost:8082/getAllProducts?currentPage=1"
```

### 3. 数据库测试
```sql
-- 插入测试数据
INSERT INTO product (product_name, price, storage_num, description) VALUES
('iPhone 15 Pro Max', 9999.00, 50, '苹果最新旗舰手机，搭载A17 Pro芯片'),
('华为Mate60 Pro', 6999.00, 80, '华为回归之作，麒麟9000S处理器');

-- 验证数据
SELECT * FROM product;
```

## 📊 使用示例

### 1. 前端调用示例
```javascript
// 获取商品列表
const getProducts = async (page = 1) => {
  try {
    const response = await get('/getAllProducts', { currentPage: page });
    console.log('商品列表:', response.data);
  } catch (error) {
    console.error('获取商品失败:', error);
  }
};

// 添加商品
const addProduct = async (productData) => {
  try {
    const response = await post('/addProduct', productData);
    console.log('添加结果:', response.message);
  } catch (error) {
    console.error('添加商品失败:', error);
  }
};
```

### 2. 业务场景示例
```java
// 库存预警
@Scheduled(fixedRate = 3600000) // 每小时检查一次
public void checkLowStock() {
    Result result = productService.getLowStockProducts(10);
    if (result.getCode() == 200) {
        List<Product> lowStockProducts = (List<Product>) result.getData();
        // 发送库存预警通知
        sendLowStockAlert(lowStockProducts);
    }
}
```

## 🔄 扩展建议

### 1. 商品分类
```sql
-- 添加分类字段
ALTER TABLE product ADD COLUMN category_id INT;
-- 创建分类表
CREATE TABLE category (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    description VARCHAR(100)
);
```

### 2. 商品图片管理
```java
// 文件上传接口
@RequestMapping("/uploadProductImage")
@RequiredToken
public Result uploadImage(@RequestParam("file") MultipartFile file, 
                         @RequestParam("productId") Integer productId) {
    // 图片上传逻辑
}
```

### 3. 商品评价系统
```sql
-- 评价表
CREATE TABLE product_review (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    user_id INT NOT NULL,
    rating INT NOT NULL,
    comment TEXT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

现在您的项目已经拥有了完整的商品管理功能！🎉
