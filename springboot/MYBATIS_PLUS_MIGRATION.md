# Mybatis到Mybatis-Plus重构说明

## 重构概述

本项目已成功将登录功能从Mybatis重构为Mybatis-Plus，提供了更强大的ORM功能和更简洁的代码实现。

## 主要变更

### 1. 依赖变更
- **移除**: `mybatis-spring-boot-starter`
- **添加**: `mybatis-plus-boot-starter` (版本: 3.5.3.1)

### 2. 实体类变更
**文件**: `src/main/java/qidian/it/springboot/entity/Admin.java`
- 添加了 `@TableName("admin")` 注解指定表名
- 添加了 `@TableId(type = IdType.AUTO)` 注解指定主键自增策略

### 3. Mapper接口变更
**文件**: `src/main/java/qidian/it/springboot/mapper/AdminMapper.java`
- 继承 `BaseMapper<Admin>` 接口
- 移除了基础CRUD方法（由Mybatis-Plus自动提供）
- 保留了自定义方法：`selectByUsername()` 和 `getAllInfo()`

### 4. XML映射文件简化
**文件**: `src/main/resources/mapping/AdminMapper.xml`
- 移除了所有基础CRUD操作的SQL语句
- 只保留自定义查询方法的SQL实现

### 5. 配置文件更新
**文件**: `src/main/resources/application.yml`
- 将 `mybatis` 配置更改为 `mybatis-plus`
- 添加了Mybatis-Plus全局配置

### 6. Service层更新
**文件**: `src/main/java/qidian/it/springboot/service/impl/AdminServiceImpl.java`
- 登录和注册功能保持不变
- 现在使用Mybatis-Plus提供的 `insert()` 方法

## Mybatis-Plus提供的新功能

### 基础CRUD操作（无需编写SQL）
- `insert(T entity)` - 插入记录
- `deleteById(Serializable id)` - 根据ID删除
- `updateById(T entity)` - 根据ID更新
- `selectById(Serializable id)` - 根据ID查询
- `selectList(Wrapper<T> queryWrapper)` - 条件查询
- 更多方法请参考Mybatis-Plus官方文档

### 条件构造器
```java
// 示例：使用条件构造器查询
QueryWrapper<Admin> queryWrapper = new QueryWrapper<>();
queryWrapper.eq("username", "admin");
Admin admin = adminMapper.selectOne(queryWrapper);
```

### 分页插件
Mybatis-Plus内置分页插件，可以轻松实现分页查询。

## 测试

### 运行单元测试
```bash
mvn test -Dtest=AdminMapperTest
```

### 测试登录接口
1. 启动应用：`mvn spring-boot:run`
2. 测试登录：
   ```
   POST http://localhost:8082/login
   参数: username=admin&password=123456
   ```

### 测试注册接口
```
POST http://localhost:8082/register
参数: username=newuser&password=newpassword
```

## 兼容性说明

- 原有的登录和注册功能完全兼容
- 前端Vue应用无需任何修改
- Redis缓存功能保持不变
- 所有自定义查询方法正常工作

## 优势

1. **代码简化**: 基础CRUD操作无需编写SQL
2. **功能增强**: 提供强大的条件构造器和分页功能
3. **性能优化**: 内置性能分析插件
4. **易于维护**: 减少XML配置，提高开发效率

## 注意事项

1. 确保数据库表结构与实体类字段对应
2. 自定义SQL查询仍需在XML文件中定义
3. 建议在生产环境部署前进行充分测试

## 下一步建议

1. 可以考虑为其他实体类（User, Employee, Department）也进行类似的Mybatis-Plus重构
2. 利用Mybatis-Plus的代码生成器自动生成基础代码
3. 使用Mybatis-Plus的分页插件优化数据查询性能
