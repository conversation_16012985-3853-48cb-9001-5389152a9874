# 清风商城电商管理系统 - 项目模块结构图

## 文字版结构图

```
                        清风商城电商管理系统
                              |
                    ┌─────────┴─────────┐
                    |                   |
                用户商城端              管理后台端
                    |                   |
        ┌───────────┼───────────┐       ┌───────────┼───────────┼───────────┐
        |           |           |       |           |           |           |
    用户管理     商品展示     订单管理   商品管理   订单管理   用户管理   数据统计
        |           |           |       |           |           |           |
    ┌───┼───┐   ┌───┼───┐   ┌───┼───┐   ┌───┼───┐   ┌───┼───┐   ┌───┼───┐   ┌───┼───┐
    |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |
  用户  密码  个人  商品  购物  支付  订单  商品  分类  库存  订单  发货  用户  权限  销量  订单
  注册  找回  中心  搜索  车   系统  跟踪  CRUD  管理  管理  处理  管理  信息  管理  统计  分析
  登录              详情  管理        状态                        查看
```

## 详细模块说明

### 一、用户商城端

#### 1. 用户管理模块
- **用户注册**：新用户账号注册，邮箱验证
- **用户登录**：身份认证，JWT Token生成
- **密码找回**：邮件验证码，密码重置
- **个人中心**：个人信息管理，收货地址

#### 2. 商品展示模块
- **商品浏览**：商品列表展示，分类筛选
- **商品搜索**：关键词搜索，搜索历史，智能建议
- **商品详情**：详细信息展示，图片查看
- **分类导航**：商品分类树形结构

#### 3. 订单管理模块
- **购物车管理**：商品添加，数量调整，批量操作
- **订单创建**：下单流程，收货信息填写
- **支付系统**：支付宝支付集成，支付状态跟踪
- **订单跟踪**：订单状态查看，物流信息

### 二、管理后台端

#### 1. 商品管理模块
- **商品CRUD**：商品增删改查，批量操作
- **分类管理**：商品分类维护，层级管理
- **库存管理**：库存监控，库存调整，预警设置
- **图片管理**：商品图片上传，图片存储

#### 2. 订单管理模块
- **订单处理**：订单审核，状态更新
- **发货管理**：发货操作，物流信息录入
- **退款处理**：退款审核，退款操作
- **订单统计**：订单数据汇总分析

#### 3. 用户管理模块
- **用户信息**：用户资料查看，状态管理
- **权限管理**：角色分配，权限控制
- **行为分析**：用户行为数据统计
- **客服管理**：用户反馈处理

#### 4. 数据统计模块
- **销量统计**：商品销量排行，趋势分析
- **订单分析**：订单量统计，收入分析
- **用户分析**：用户增长，活跃度统计
- **运营报表**：综合运营数据报告

## 技术架构支撑

### 前端技术栈
- **Vue3**：前端框架，Composition API
- **Element Plus**：UI组件库
- **Vue Router**：路由管理，权限控制
- **Axios**：HTTP请求库

### 后端技术栈
- **Spring Boot**：后端框架
- **MyBatis Plus**：ORM框架
- **MySQL**：数据存储
- **JWT**：身份认证

### 核心服务
- **支付服务**：支付宝SDK集成
- **邮件服务**：Spring Boot Mail
- **文件服务**：图片上传存储
- **安全服务**：权限控制，数据加密

## 数据流向

```
用户操作 → 前端界面 → API接口 → 业务逻辑 → 数据库操作 → 返回结果 → 前端展示
```

## 系统特点

1. **前后端分离**：独立开发，松耦合设计
2. **模块化架构**：功能模块独立，易于维护
3. **权限控制**：多角色权限管理
4. **安全保障**：数据加密，支付安全
5. **性能优化**：缓存机制，数据库优化
6. **用户体验**：响应式设计，交互友好
