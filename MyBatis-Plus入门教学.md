# MyBatis Plus 入门教学 - 基于清风商城项目

## 📚 目录
1. [MyBatis Plus 简介](#1-mybatis-plus-简介)
2. [项目配置](#2-项目配置)
3. [实体类设计](#3-实体类设计)
4. [Mapper 接口](#4-mapper-接口)
5. [Service 层](#5-service-层)
6. [常用查询操作](#6-常用查询操作)
7. [高级功能](#7-高级功能)
8. [实战案例](#8-实战案例)

---

## 1. MyBatis Plus 简介

### 什么是 MyBatis Plus？
MyBatis Plus（简称 MP）是一个 MyBatis 的增强工具，在 MyBatis 的基础上只做增强不做改变，为简化开发、提高效率而生。

### 核心特性
- **无侵入**：只做增强不做改变，引入它不会对现有工程产生影响
- **损耗小**：启动即会自动注入基本 CURD，性能基本无损耗
- **强大的 CRUD 操作**：内置通用 Mapper、通用 Service
- **支持 Lambda 形式调用**：通过 Lambda 表达式，方便的编写各类查询条件
- **支持主键自动生成**：支持多达 4 种主键策略
- **内置分页插件**：基于 MyBatis 物理分页，开发者无需关心具体操作

---

## 2. 项目配置

### 2.1 Maven 依赖配置

在清风商城项目的 `pom.xml` 中添加 MyBatis Plus 依赖：

```xml
<dependencies>
    <!-- MyBatis Plus -->
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>3.5.3.1</version>
    </dependency>
    
    <!-- MySQL Driver -->
    <dependency>
        <groupId>com.mysql</groupId>
        <artifactId>mysql-connector-j</artifactId>
        <version>8.0.33</version>
        <scope>runtime</scope>
    </dependency>
</dependencies>
```

### 2.2 配置文件设置

在 `application.yml` 中配置 MyBatis Plus：

```yaml
# 数据源配置
spring:
  datasource:
    username: root
    password: 123456
    url: ***********************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver

# MyBatis Plus 配置
mybatis-plus:
  # 指定 mapper.xml 的位置
  mapper-locations: classpath:mapping/*.xml
  # 扫描实体类的位置
  type-aliases-package: qidian.it.springboot.entity
  configuration:
    # 默认开启驼峰命名法
    map-underscore-to-camel-case: true
    # 打印SQL日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 主键策略：自增
      id-type: auto
      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
```

### 2.3 启动类配置

在主启动类上添加 `@MapperScan` 注解：

```java
@SpringBootApplication
@MapperScan("qidian.it.springboot.mapper")
public class SpringbootApplication {
    public static void main(String[] args) {
        SpringApplication.run(SpringbootApplication.class, args);
    }
}
```

---

## 3. 实体类设计

### 3.1 基本注解使用

以清风商城的 `Product` 实体类为例：

```java
@TableName("product")  // 指定数据库表名
public class Product {
    
    /**
     * 主键ID - 自增策略
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 商品名称 - 指定数据库字段名
     */
    @TableField("name")
    private String name;
    
    /**
     * 分类ID
     */
    @TableField("category_id")
    private Long categoryId;
    
    /**
     * 商品价格
     */
    @TableField("price")
    private BigDecimal price;
    
    /**
     * 创建时间 - JSON格式化
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    // 构造方法、getter、setter...
}
```

### 3.2 常用注解说明

| 注解 | 说明 | 示例 |
|------|------|------|
| `@TableName` | 指定表名 | `@TableName("product")` |
| `@TableId` | 主键注解 | `@TableId(type = IdType.AUTO)` |
| `@TableField` | 字段注解 | `@TableField("user_name")` |
| `@TableLogic` | 逻辑删除 | `@TableLogic` |

### 3.3 主键策略

```java
public enum IdType {
    AUTO(0),        // 数据库自增
    NONE(1),        // 无状态
    INPUT(2),       // 手动输入
    ASSIGN_ID(3),   // 分配ID (主键类型为number或string)
    ASSIGN_UUID(4); // 分配UUID (主键类型为string)
}
```

---

## 4. Mapper 接口

### 4.1 基础 Mapper

继承 `BaseMapper<T>` 接口，自动获得基础 CRUD 方法：

```java
@Mapper
public interface ProductMapper extends BaseMapper<Product> {
    // 继承BaseMapper后，自动拥有以下方法：
    // insert(T entity)
    // deleteById(Serializable id)
    // updateById(T entity)
    // selectById(Serializable id)
    // selectList(Wrapper<T> queryWrapper)
    // 等等...
}
```

### 4.2 自定义查询方法

使用 `@Select` 注解编写自定义 SQL：

```java
@Mapper
public interface ProductMapper extends BaseMapper<Product> {
    
    /**
     * 查询所有商品及其分类信息
     */
    @Select("SELECT p.*, c.name as category_name " +
            "FROM product p " +
            "LEFT JOIN category c ON p.category_id = c.id " +
            "ORDER BY p.id")
    List<Map<String, Object>> selectProductsWithCategory();
    
    /**
     * 根据商品名称模糊查询
     */
    @Select("SELECT * FROM product WHERE name LIKE CONCAT('%', #{name}, '%') ORDER BY id")
    List<Product> selectByNameLike(String name);
    
    /**
     * 查询商品及其库存信息
     */
    @Select("SELECT p.*, s.quantity, s.min_stock, " +
            "CASE WHEN s.quantity = 0 THEN '缺货' " +
            "     WHEN s.quantity <= s.min_stock THEN '库存不足' " +
            "     ELSE '库存充足' END as stock_status " +
            "FROM product p " +
            "LEFT JOIN stock s ON p.id = s.product_id " +
            "ORDER BY p.id")
    List<Map<String, Object>> selectProductsWithStock();
}
```

---

## 5. Service 层

### 5.1 Service 接口

继承 `IService<T>` 接口：

```java
public interface ProductService extends IService<Product> {
    
    // 自定义业务方法
    List<Map<String, Object>> getProductsWithCategory();
    List<Product> getOnlineProducts();
    List<Product> searchProductsByName(String name);
    boolean addProduct(Product product);
}
```

### 5.2 Service 实现类

继承 `ServiceImpl<M, T>` 类：

```java
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Autowired
    private ProductMapper productMapper;
    
    @Autowired
    private StockService stockService;

    @Override
    public List<Map<String, Object>> getProductsWithCategory() {
        return productMapper.selectProductsWithCategory();
    }

    @Override
    public List<Product> getOnlineProducts() {
        QueryWrapper<Product> wrapper = new QueryWrapper<>();
        wrapper.eq("status", 1);  // 查询上架商品
        return list(wrapper);
    }

    @Override
    public List<Product> searchProductsByName(String name) {
        return productMapper.selectByNameLike(name);
    }

    @Override
    @Transactional
    public boolean addProduct(Product product) {
        // 保存商品信息
        boolean productSaved = save(product);
        
        if (productSaved) {
            // 创建对应的库存记录
            Stock stock = new Stock(product.getId(), 0);
            stockService.save(stock);
        }
        
        return productSaved;
    }
}
```

---

## 6. 常用查询操作

### 6.1 基础 CRUD 操作

```java
// 1. 插入
Product product = new Product();
product.setName("iPhone 15");
product.setPrice(new BigDecimal("7999.00"));
productService.save(product);

// 2. 根据ID查询
Product product = productService.getById(1L);

// 3. 查询所有
List<Product> products = productService.list();

// 4. 更新
product.setPrice(new BigDecimal("7599.00"));
productService.updateById(product);

// 5. 删除
productService.removeById(1L);
```

### 6.2 条件查询 - QueryWrapper

```java
// 查询价格大于1000的商品
QueryWrapper<Product> wrapper = new QueryWrapper<>();
wrapper.gt("price", 1000);
List<Product> products = productService.list(wrapper);

// 查询名称包含"手机"的商品
QueryWrapper<Product> wrapper = new QueryWrapper<>();
wrapper.like("name", "手机");
List<Product> products = productService.list(wrapper);

// 复杂条件查询
QueryWrapper<Product> wrapper = new QueryWrapper<>();
wrapper.eq("status", 1)                    // 状态为1
       .gt("price", 100)                   // 价格大于100
       .like("name", "手机")               // 名称包含"手机"
       .orderByDesc("create_time");        // 按创建时间降序
List<Product> products = productService.list(wrapper);
```

### 6.3 Lambda 查询 - LambdaQueryWrapper

```java
// Lambda 方式查询，避免字段名写错
LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
wrapper.eq(Product::getStatus, 1)
       .gt(Product::getPrice, new BigDecimal("100"))
       .like(Product::getName, "手机")
       .orderByDesc(Product::getCreateTime);
List<Product> products = productService.list(wrapper);
```

### 6.4 更新操作 - UpdateWrapper

```java
// 批量更新
UpdateWrapper<Product> updateWrapper = new UpdateWrapper<>();
updateWrapper.eq("category_id", 1001)
             .set("status", 0);  // 将分类ID为1001的商品下架
productService.update(updateWrapper);

// Lambda 方式更新
LambdaUpdateWrapper<Product> updateWrapper = new LambdaUpdateWrapper<>();
updateWrapper.eq(Product::getCategoryId, 1001L)
             .set(Product::getStatus, 0);
productService.update(updateWrapper);
```

---

## 7. 高级功能

### 7.1 分页查询

```java
// 分页查询商品
Page<Product> page = new Page<>(1, 10);  // 第1页，每页10条
QueryWrapper<Product> wrapper = new QueryWrapper<>();
wrapper.eq("status", 1);
Page<Product> productPage = productService.page(page, wrapper);

System.out.println("总记录数：" + productPage.getTotal());
System.out.println("总页数：" + productPage.getPages());
System.out.println("当前页数据：" + productPage.getRecords());
```

### 7.2 批量操作

```java
// 批量插入
List<Product> products = Arrays.asList(
    new Product("商品1", 1001L, new BigDecimal("99.00"), "描述1", "url1"),
    new Product("商品2", 1002L, new BigDecimal("199.00"), "描述2", "url2")
);
productService.saveBatch(products);

// 批量删除
List<Long> ids = Arrays.asList(1L, 2L, 3L);
productService.removeByIds(ids);
```

### 7.3 统计查询

```java
// 统计商品总数
QueryWrapper<Product> wrapper = new QueryWrapper<>();
wrapper.eq("status", 1);
long count = productService.count(wrapper);

// 检查是否存在
boolean exists = productService.lambdaQuery()
    .eq(Product::getName, "iPhone 15")
    .exists();
```

---

## 8. 实战案例

### 8.1 用户登录验证

```java
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Override
    public User getUserByEmail(String email) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("email", email);
        return getOne(wrapper);
    }

    @Override
    public User getUserByUsername(String username) {
        return lambdaQuery()
            .eq(User::getUsername, username)
            .one();
    }
}
```

### 8.2 购物车查询

```java
@Mapper
public interface CartMapper extends BaseMapper<Cart> {
    
    /**
     * 查询用户购物车（包含商品信息）
     */
    @Select("SELECT c.id, c.user_id, c.product_id, c.quantity, c.create_time, " +
            "p.name, p.description, p.price, p.image_url, " +
            "cat.name as category_name " +
            "FROM cart c " +
            "INNER JOIN product p ON c.product_id = p.id " +
            "LEFT JOIN category cat ON p.category_id = cat.id " +
            "WHERE c.user_id = #{userId} " +
            "ORDER BY c.create_time DESC")
    List<Map<String, Object>> selectCartWithProductByUserId(Long userId);
}
```

### 8.3 库存管理

```java
@Service
public class StockServiceImpl extends ServiceImpl<StockMapper, Stock> implements StockService {

    @Override
    public boolean checkStockAvailable(Long productId, Integer quantity) {
        Stock stock = lambdaQuery()
            .eq(Stock::getProductId, productId)
            .one();
        
        return stock != null && stock.getQuantity() >= quantity;
    }

    @Override
    @Transactional
    public boolean reduceStock(Long productId, Integer quantity) {
        LambdaUpdateWrapper<Stock> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Stock::getProductId, productId)
                    .setSql("quantity = quantity - " + quantity)
                    .ge(Stock::getQuantity, quantity);  // 确保库存充足
        
        return update(updateWrapper);
    }
}
```

## 🎯 总结

MyBatis Plus 通过以下方式简化了开发：

1. **减少代码量**：自动生成基础 CRUD 方法
2. **类型安全**：Lambda 表达式避免字段名错误
3. **功能丰富**：内置分页、批量操作、条件构造器等
4. **易于扩展**：支持自定义 SQL 和复杂查询

在清风商城项目中，MyBatis Plus 帮助我们：
- 快速实现用户、商品、订单等实体的 CRUD 操作
- 通过条件构造器实现复杂的业务查询
- 使用事务注解保证数据一致性
- 通过自定义 SQL 实现复杂的关联查询

掌握 MyBatis Plus 将大大提高你的开发效率！🚀

---

## 9. 常见问题与解决方案

### 9.1 字段映射问题

**问题**：数据库字段名与实体类属性名不一致

**解决方案**：
```java
public class User {
    @TableField("user_name")  // 数据库字段名
    private String username;  // Java属性名
}
```

### 9.2 时间字段处理

**问题**：时间字段的自动填充和格式化

**解决方案**：
```java
public class Product {
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
```

### 9.3 逻辑删除配置

**问题**：需要实现软删除功能

**解决方案**：
```java
public class Product {
    @TableLogic
    @TableField("deleted")
    private Integer deleted;  // 0-未删除，1-已删除
}
```

### 9.4 分页插件配置

**问题**：分页查询不生效

**解决方案**：添加分页插件配置
```java
@Configuration
public class MybatisPlusConfig {

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }
}
```

---

## 10. 性能优化建议

### 10.1 合理使用索引

```sql
-- 为常用查询字段添加索引
CREATE INDEX idx_product_category ON product(category_id);
CREATE INDEX idx_product_status ON product(status);
CREATE INDEX idx_user_email ON user(email);
```

### 10.2 避免 N+1 查询

**不推荐**：
```java
List<Product> products = productService.list();
for (Product product : products) {
    Category category = categoryService.getById(product.getCategoryId());
    // 这会产生 N+1 查询问题
}
```

**推荐**：
```java
// 使用关联查询一次性获取数据
List<Map<String, Object>> productsWithCategory = productMapper.selectProductsWithCategory();
```

### 10.3 合理使用批量操作

```java
// 批量插入比循环插入效率更高
List<Product> products = new ArrayList<>();
// ... 添加数据
productService.saveBatch(products, 1000);  // 每批1000条
```

---

## 11. 调试技巧

### 11.1 开启 SQL 日志

在 `application.yml` 中配置：
```yaml
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
```

### 11.2 使用 P6Spy 监控 SQL

添加依赖：
```xml
<dependency>
    <groupId>p6spy</groupId>
    <artifactId>p6spy</artifactId>
    <version>3.9.1</version>
</dependency>
```

修改数据源配置：
```yaml
spring:
  datasource:
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    url: *************************************
```

---

## 12. 最佳实践

### 12.1 实体类设计规范

```java
@TableName("product")
@Data  // 使用Lombok简化代码
@NoArgsConstructor
@AllArgsConstructor
public class Product {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("name")
    @NotBlank(message = "商品名称不能为空")
    private String name;

    @TableField("price")
    @DecimalMin(value = "0.01", message = "价格必须大于0")
    private BigDecimal price;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
```

### 12.2 Service 层设计规范

```java
@Service
@Transactional(rollbackFor = Exception.class)
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Override
    public boolean addProduct(Product product) {
        // 1. 参数校验
        if (product == null || StringUtils.isBlank(product.getName())) {
            throw new BusinessException("商品信息不完整");
        }

        // 2. 业务逻辑处理
        product.setCreateTime(LocalDateTime.now());
        product.setStatus(1);  // 默认上架

        // 3. 数据保存
        boolean saved = save(product);

        // 4. 后续处理（如创建库存记录）
        if (saved) {
            Stock stock = new Stock(product.getId(), 0);
            stockService.save(stock);
        }

        return saved;
    }
}
```

### 12.3 异常处理

```java
@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(DuplicateKeyException.class)
    public Result handleDuplicateKeyException(DuplicateKeyException e) {
        return Result.error("数据重复，请检查后重试");
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public Result handleDataIntegrityViolationException(DataIntegrityViolationException e) {
        return Result.error("数据完整性约束违反");
    }
}
```

---

## 🎓 学习建议

1. **从基础开始**：先掌握基本的 CRUD 操作
2. **多练习**：通过实际项目练习各种查询场景
3. **阅读源码**：了解 MyBatis Plus 的实现原理
4. **关注性能**：学会分析和优化 SQL 性能
5. **持续学习**：关注 MyBatis Plus 的新特性和最佳实践

## 📚 参考资料

- [MyBatis Plus 官方文档](https://baomidou.com/)
- [MyBatis Plus GitHub](https://github.com/baomidou/mybatis-plus)
- [清风商城项目源码](./product-management-system)

---

**恭喜你完成了 MyBatis Plus 的入门学习！现在你可以在清风商城项目中熟练使用 MyBatis Plus 进行数据库操作了。记住，实践是最好的老师，多写代码，多思考，你会越来越熟练！** 💪
