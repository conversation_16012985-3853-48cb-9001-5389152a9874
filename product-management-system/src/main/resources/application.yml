server:
  port: 8082

spring:
  application:
    name: springboot
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  datasource:
    username: root
    password: 123456
    url: ***********************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-timeout: 6000
      maximum-pool-size: 5
  # 邮件配置
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: tavqphcwdijpcgfa
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            trust: smtp.qq.com
    default-encoding: UTF-8


mybatis-plus:
  # 指定 mapper.xml 的位置（保留自定义SQL）
  mapper-locations: classpath:mapping/*.xml
  #扫描实体类的位置
  type-aliases-package: qidian.it.springboot.entity
  configuration:
    #默认开启驼峰命名法
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 主键策略：自增
      id-type: auto
      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0

# JWT配置
config:
  jwt:
    # 加密密钥
    secret: abcdefg1234213213123123123123123123123213123123125gdfgdffdgfgdfdggfd231231232142141412312333322121212222222222222111222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222221111111111sadasdasdasdasdasdsadsadasdsadsadasdasdasdasdsad
    # token有效时长（秒）
    expire: 3600
    # header 名称
    header: token


alipay:
  appId: 9021000151671457
  appPrivateKey: MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDB6s4JFHwaxumFRrunIbY48Y7CfOU9yhjigSqHfNpqlUbv7hqY6taeeJPbQ7rAXlgZIN1lKHYS059KXEEMfQDIssuGXo8TusF1dUc0FW5sWrrqRJcm0MPmBRogvM7GMyirqmRPSQszzIGnhA0TQ+tK3FJMBRAqoR6A0whIDLguDS6/aIC5U9qUZ0V/LmrTPoU4vbltX8pcUtf2zXcvwJ+XxgPlZidvoNzrplWcFmrI8XNOJzN/F9VKlmd3wLFIJLXoUdXnd9cals/EHt8QsSyDRabOQMYUusto61Rs1AzGAbENBiNSm+IhO51kUvAQIvZNE2b+9cUd6xi9iaExc0tJAgMBAAECggEBAJE/DpQ+g7aKxt7M3EGTPqnoRMXoRszBU2JU3UYefnrW1QthjQp828RXDGhvkrID+HjZ5PqgE9CGMgnAj/iKqMurIEbFDOX6PG1+KpcYdl0ymE5gkdbBB/qCV8+tnDNYnl32gqvgSkPCKlsOzbxgYxzvabUHFdTYWcCkUM+mt03Fws/BNgYxPi58GaZYlspU0j1QPHY2aajhVa0x2SMYS1pUbMKxMHnErLu8fBSOPga2IRm+T4eRYCh3zkPSrQ3OMjE9XrPlFbyrynSr+F1ZGmWc6Awt9MqoVwftjYl1LF51EBe/Rawf17W+ijj3mNj3dZXRLOIpJiQ79J+fcq8VYAECgYEA9DgyzZ7RbAveNL+jpdcVVU87wt0QS/B0f09OoXEgJ4XdB/cLJHDTpUqmLvQFYgiAFEcg/USzsqtuybxFNVVpJodZvYFM4KyZ06UR6AdfEZq/L/TeRTlac7rThBtal/undCplQivHZdr2s8GIkChJM12RhlM1YS7ux6zTI1kVCHECgYEAy0Vvvr8vom+IWv+ygLbX7ruaBuJ8YuOiRo4XAAYhD6ucg6dwxgaI7YfAxxwp+Emu6R/knqqzB/POjGYzIqmpiV+4QzK/cGmkKliCN2evmmoSk1bEStM2x5MIrqeSird8tPmUCvrpKLhNQcuAL2xFfhwybUDUtPgnPjr/KFMnHFkCgYEAg/jGg1T6iG+VYV3Z+ilTbulFOxUo1FCZM57Qt+8g2Bq/ialXTRbnWCAR07esHQDI7axOSG7SyI9ZbAygPaTtEJP9y6lOuO6spbFDDubE2geMn0aswi2vEnD4EiCSASVNpTmvxGVR+FmuDQ9dU8FhZS+AD11xsBWjlSVwZ16hPhECgYAPsCI5tIO2o6kAbO+X44Sy1iPDGEPFVhA2Jc08oTx3+ySvPIMFpYd33ZxRQakzkmw/bci1PjNnD7gHOfQEXtfI1GxiM053HJAg/IfAQu5Ymosod+/aXobBzGVFJYW30wV66jA00Lyh+edshoo0HsUWQJLbv78q4GQW93Kt6A0CsQKBgQDVB3AJleurxDJt01A2SFpXTC2vmzo75nkDFslLwrfn9JyPCIZDABFb+MrMg7EIkIaZTlVfnfspDoN+SHgWLqevFX7I1voeY9NwGXik9k0LU+tDNvj932RFkxdtEWgxmyAnKmHvNZV8cgnYR12OkGdI2Lt4lYIIzMvvCYa6cFyaPg==
  alipayPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2sLCujue4tRzyKln/OmQDHOH2fjjvMFSxL7Z46aWD3vVXneILfF0F9IqdjRtk27zP1qOCTLmx+EUuBm6fSFLxEGhfcoMws9W6DHyj1IIjnenIMBlrwCnbnto4j1g31SPp920VdqOEYJDB6ldGIS/oadYy9ZwOjKrtnPw1nv9XB2up4U1Mrn15VV6U5GwKzdjbYZnxpKW1Dx6cWvO5y1g5jtDYQm1Dgf8yMBAt14/io2Iuzx8xZVIOSWDCsUd0MPGG7ZE7JmiYMC/VwA8W12Hg/wAIHZV6X9SjlhIpJffNiioOieK+fYZdjVZP6Wul3MuShPYH8mFx3VUOgdhr0Xr3wIDAQAB
  notifyUrl: http://localhost:8082/api/alipay/notify



