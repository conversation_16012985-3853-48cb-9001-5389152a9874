-- 商品管理系统 - 商品相关表结构和测试数据
-- 创建时间：2025-09-02

-- ========================================
-- 1. 商品分类表
-- ========================================
CREATE TABLE category (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '商品分类表';

-- ========================================
-- 2. 商品表
-- ========================================
CREATE TABLE product (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '商品ID',
    name VARCHAR(200) NOT NULL COMMENT '商品名称',
    category_id BIGINT NOT NULL COMMENT '分类ID',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    description TEXT COMMENT '商品描述',
    image_url VARCHAR(500) COMMENT '商品图片地址',
    status TINYINT DEFAULT 1 COMMENT '状态：1上架，0下架',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (category_id) REFERENCES category(id)
) COMMENT '商品表';

-- ========================================
-- 3. 商品库存表
-- ========================================
CREATE TABLE stock (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '库存ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    quantity INT NOT NULL DEFAULT 0 COMMENT '库存数量',
    min_stock INT DEFAULT 10 COMMENT '最低库存预警',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (product_id) REFERENCES product(id)
) COMMENT '商品库存表';

-- ========================================
-- 插入商品分类测试数据
-- ========================================
INSERT INTO category (id, name) VALUES
(1001, '酒类'),
(1002, '饮料'),
(1003, '零食'),
(1004, '日用品'),
(1005, '电子产品'),
(1006, '服装'),
(1007, '图书'),
(1008, '运动用品'),
(1009, '美妆护肤'),
(1010, '家居用品');

-- ========================================
-- 插入商品测试数据
-- ========================================
INSERT INTO product (id, name, category_id, price, description, image_url, status) VALUES
-- 酒类商品
(10001, '茅台（MOUTAI）飞天 酱香型白酒 53度 200ml 单瓶装', 1001, 299.00, '茅台（MOUTAI）飞天 酱香型白酒 53度 200ml 单瓶装，正宗茅台酒，酱香浓郁', '/images/maotai_200ml.jpg', 1),
(10002, '五粮液 52度 500ml 浓香型白酒', 1001, 899.00, '五粮液经典装，五种粮食酿造，口感醇厚', '/images/wuliangye_500ml.jpg', 1),
(10003, '剑南春 52度 500ml 浓香型白酒', 1001, 368.00, '剑南春，千年老窖万年糟，酒好还需窖池老', '/images/jiannanchun_500ml.jpg', 1),
(10004, '青岛啤酒 经典装 500ml*12瓶', 1001, 45.00, '青岛啤酒经典装，清爽口感，聚会必备', '/images/qingdao_beer.jpg', 1),

-- 饮料商品
(10005, '可口可乐 330ml*24罐装', 1002, 48.00, '可口可乐经典装，畅爽一夏', '/images/coca_cola.jpg', 1),
(10006, '农夫山泉 天然水 550ml*24瓶', 1002, 36.00, '农夫山泉有点甜，天然弱碱性水', '/images/nongfu_water.jpg', 1),
(10007, '红牛 维生素功能饮料 250ml*24罐', 1002, 96.00, '红牛能量饮料，补充维生素和能量', '/images/redbull.jpg', 1),

-- 零食商品
(10008, '三只松鼠 坚果大礼包 1314g', 1003, 89.00, '三只松鼠坚果大礼包，多种坚果组合装', '/images/squirrel_nuts.jpg', 1),
(10009, '良品铺子 肉脯组合装 500g', 1003, 68.00, '良品铺子精选肉脯，多种口味', '/images/liangpin_meat.jpg', 1),
(10010, '奥利奥 夹心饼干 原味 390g', 1003, 15.80, '奥利奥经典原味夹心饼干', '/images/oreo_cookies.jpg', 1),

-- 日用品商品
(10011, '舒肤佳 香皂 纯白清香型 125g*6块', 1004, 24.90, '舒肤佳香皂，温和清洁，呵护肌肤', '/images/safeguard_soap.jpg', 1),
(10012, '海飞丝 去屑洗发水 400ml', 1004, 32.90, '海飞丝去屑洗发水，有效去屑止痒', '/images/headshoulders_shampoo.jpg', 1),

-- 电子产品商品
(10013, '小米 充电宝 10000mAh 快充版', 1005, 79.00, '小米充电宝，大容量快充，出行必备', '/images/xiaomi_powerbank.jpg', 1),
(10014, '苹果 iPhone 数据线 1米', 1005, 149.00, '苹果原装数据线，快速充电传输', '/images/apple_cable.jpg', 1),

-- 服装商品
(10015, '优衣库 纯棉T恤 男款 白色 L码', 1006, 59.00, '优衣库经典纯棉T恤，舒适透气', '/images/uniqlo_tshirt.jpg', 1),
(10016, '李宁 运动鞋 男款 42码 黑色', 1006, 299.00, '李宁专业运动鞋，舒适耐穿', '/images/lining_shoes.jpg', 1);

-- ========================================
-- 插入商品库存测试数据
-- ========================================
INSERT INTO stock (product_id, quantity, min_stock) VALUES
-- 酒类库存
(10001, 50, 10),   -- 茅台 200ml
(10002, 30, 5),    -- 五粮液 500ml
(10003, 25, 5),    -- 剑南春 500ml
(10004, 100, 20),  -- 青岛啤酒

-- 饮料库存
(10005, 200, 50),  -- 可口可乐
(10006, 150, 30),  -- 农夫山泉
(10007, 80, 20),   -- 红牛

-- 零食库存
(10008, 60, 15),   -- 三只松鼠坚果
(10009, 45, 10),   -- 良品铺子肉脯
(10010, 120, 30),  -- 奥利奥饼干

-- 日用品库存
(10011, 200, 50),  -- 舒肤佳香皂
(10012, 80, 20),   -- 海飞丝洗发水

-- 电子产品库存
(10013, 40, 10),   -- 小米充电宝
(10014, 60, 15),   -- 苹果数据线

-- 服装库存
(10015, 100, 25),  -- 优衣库T恤
(10016, 35, 8);    -- 李宁运动鞋

-- ========================================
-- 查询语句示例
-- ========================================

-- 查询所有商品分类
-- SELECT * FROM category ORDER BY id;

-- 查询所有商品及其分类信息
-- SELECT p.*, c.name as category_name 
-- FROM product p 
-- LEFT JOIN category c ON p.category_id = c.id 
-- ORDER BY p.id;

-- 查询所有商品及其库存信息
-- SELECT p.name, p.price, s.quantity, s.min_stock,
--        CASE WHEN s.quantity <= s.min_stock THEN '库存不足' ELSE '库存充足' END as stock_status
-- FROM product p 
-- LEFT JOIN stock s ON p.id = s.product_id 
-- ORDER BY p.id;

-- 查询库存不足的商品
-- SELECT p.name, p.price, s.quantity, s.min_stock
-- FROM product p 
-- INNER JOIN stock s ON p.id = s.product_id 
-- WHERE s.quantity <= s.min_stock
-- ORDER BY s.quantity;

-- 按分类统计商品数量
-- SELECT c.name as category_name, COUNT(p.id) as product_count
-- FROM category c 
-- LEFT JOIN product p ON c.id = p.category_id 
-- GROUP BY c.id, c.name 
-- ORDER BY product_count DESC;
