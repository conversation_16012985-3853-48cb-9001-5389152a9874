-- 快速插入商品测试数据脚本
-- 注意：执行前请确保已创建相关表结构

-- 插入商品分类
INSERT INTO category (id, name) VALUES
(1001, '酒类'),
(1002, '饮料'),
(1003, '零食'),
(1004, '日用品'),
(1005, '电子产品');

-- 插入商品数据
INSERT INTO product (id, name, category_id, price, description, image_url, status) VALUES
(10001, '茅台（MOUTAI）飞天 酱香型白酒 53度 200ml 单瓶装', 1001, 299.00, '茅台（MOUTAI）飞天 酱香型白酒 53度 200ml 单瓶装', null, 1),
(10002, '五粮液 52度 500ml 浓香型白酒', 1001, 899.00, '五粮液经典装，五种粮食酿造', null, 1),
(10003, '可口可乐 330ml*24罐装', 1002, 48.00, '可口可乐经典装，畅爽一夏', null, 1),
(10004, '三只松鼠 坚果大礼包 1314g', 1003, 89.00, '三只松鼠坚果大礼包，多种坚果组合', null, 1),
(10005, '小米 充电宝 10000mAh 快充版', 1005, 79.00, '小米充电宝，大容量快充', null, 1);

-- 插入库存数据
INSERT INTO stock (product_id, quantity) VALUES
(10001, 50),
(10002, 30),
(10003, 200),
(10004, 60),
(10005, 40);

-- 验证数据插入
SELECT '=== 商品分类 ===' as info;
SELECT * FROM category;

SELECT '=== 商品信息 ===' as info;
SELECT p.id, p.name, c.name as category_name, p.price, p.status 
FROM product p 
LEFT JOIN category c ON p.category_id = c.id;

SELECT '=== 库存信息 ===' as info;
SELECT p.name, s.quantity 
FROM product p 
LEFT JOIN stock s ON p.id = s.product_id;
