package qidian.it.springboot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 邮箱验证码实体类
 */
@Data
@TableName("email_verification")
public class EmailVerification {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 邮箱地址
     */
    @TableField("email")
    private String email;
    
    /**
     * 验证码
     */
    @TableField("code")
    private String code;
    
    /**
     * 验证码类型：1-忘记密码，2-注册验证
     */
    @TableField("type")
    private Integer type;
    
    /**
     * 过期时间
     */
    @TableField("expire_time")
    private LocalDateTime expireTime;
    
    /**
     * 是否已使用：0-未使用，1-已使用
     */
    @TableField("used")
    private Integer used;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 验证码类型常量
     */
    public static class Type {
        public static final int FORGOT_PASSWORD = 1;
        public static final int REGISTER = 2;
    }
    
    /**
     * 使用状态常量
     */
    public static class Status {
        public static final int UNUSED = 0;
        public static final int USED = 1;
    }
}
