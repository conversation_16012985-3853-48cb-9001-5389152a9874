package qidian.it.springboot.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * JWT Token验证注解
 * 在需要验证token的方法上添加此注解
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequiredToken {
    /**
     * 注解功能：方法上加了该注解，需要验证请求的token
     */
}
