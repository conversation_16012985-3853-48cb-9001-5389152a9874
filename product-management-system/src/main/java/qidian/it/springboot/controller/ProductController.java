package qidian.it.springboot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import qidian.it.springboot.entity.Category;
import qidian.it.springboot.entity.Product;
import qidian.it.springboot.entity.Stock;
import qidian.it.springboot.service.CategoryService;
import qidian.it.springboot.service.ProductService;
import qidian.it.springboot.service.StockService;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商品管理控制器
 */
@RestController
@RequestMapping("/api/product")
public class ProductController {

    @Autowired
    private ProductService productService;
    
    @Autowired
    private CategoryService categoryService;
    
    @Autowired
    private StockService stockService;

    /**
     * 获取所有商品分类
     */
    @GetMapping("/categories")
    public Map<String, Object> getAllCategories() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Category> categories = categoryService.getAllCategories();
            result.put("success", true);
            result.put("data", categories);
            result.put("message", "获取分类列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取分类列表失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 添加商品分类
     */
    @PostMapping("/category")
    public Map<String, Object> addCategory(@RequestBody Category category) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (category.getName() == null || category.getName().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "分类名称不能为空");
                return result;
            }

            boolean success = categoryService.addCategory(category);
            if (success) {
                result.put("success", true);
                result.put("message", "添加分类成功");
                result.put("data", category);
            } else {
                result.put("success", false);
                result.put("message", "分类名称已存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "添加分类失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 更新商品分类
     */
    @PutMapping("/category")
    public Map<String, Object> updateCategory(@RequestBody Category category) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (category.getId() == null) {
                result.put("success", false);
                result.put("message", "分类ID不能为空");
                return result;
            }

            if (category.getName() == null || category.getName().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "分类名称不能为空");
                return result;
            }

            boolean success = categoryService.updateCategory(category);
            if (success) {
                result.put("success", true);
                result.put("message", "更新分类成功");
                result.put("data", category);
            } else {
                result.put("success", false);
                result.put("message", "分类名称已被其他分类使用");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新分类失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 删除商品分类
     */
    @DeleteMapping("/category/{categoryId}")
    public Map<String, Object> deleteCategory(@PathVariable Long categoryId) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 检查该分类下是否有商品
            Integer productCount = categoryService.getProductCountByCategory(categoryId);
            if (productCount > 0) {
                result.put("success", false);
                result.put("message", "该分类下还有 " + productCount + " 个商品，无法删除");
                return result;
            }

            boolean success = categoryService.deleteCategory(categoryId);
            if (success) {
                result.put("success", true);
                result.put("message", "删除分类成功");
            } else {
                result.put("success", false);
                result.put("message", "删除分类失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除分类失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取分类详情（包含商品数量）
     */
    @GetMapping("/category/{categoryId}/detail")
    public Map<String, Object> getCategoryDetail(@PathVariable Long categoryId) {
        Map<String, Object> result = new HashMap<>();
        try {
            Category category = categoryService.getById(categoryId);
            if (category == null) {
                result.put("success", false);
                result.put("message", "分类不存在");
                return result;
            }

            Integer productCount = categoryService.getProductCountByCategory(categoryId);

            Map<String, Object> categoryDetail = new HashMap<>();
            categoryDetail.put("id", category.getId());
            categoryDetail.put("name", category.getName());
            categoryDetail.put("productCount", productCount);

            result.put("success", true);
            result.put("data", categoryDetail);
            result.put("message", "获取分类详情成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取分类详情失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取所有商品（包含分类和库存信息）- 管理员使用
     */
    @GetMapping("/list")
    public Map<String, Object> getProductList() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Map<String, Object>> products = productService.getProductsWithStock();
            result.put("success", true);
            result.put("data", products);
            result.put("message", "获取商品列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取商品列表失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取上架商品列表（包含分类信息）- 普通用户使用
     */
    @GetMapping("/online")
    public Map<String, Object> getOnlineProductList() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Map<String, Object>> products = productService.getOnlineProductsWithCategory();
            result.put("success", true);
            result.put("data", products);
            result.put("message", "获取上架商品列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取上架商品列表失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取商品及库存信息
     */
    @GetMapping("/list-with-stock")
    public Map<String, Object> getProductListWithStock() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Map<String, Object>> products = productService.getProductsWithStock();
            result.put("success", true);
            result.put("data", products);
            result.put("message", "获取商品库存信息成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取商品库存信息失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取商品详情（包含库存信息）
     */
    @GetMapping("/{productId}")
    public Map<String, Object> getProductDetail(@PathVariable Long productId) {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> productWithStock = productService.getProductWithStockById(productId);
            if (productWithStock != null) {
                result.put("success", true);
                result.put("data", productWithStock);
                result.put("message", "获取商品详情成功");
            } else {
                result.put("success", false);
                result.put("message", "商品不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取商品详情失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 根据分类获取商品
     */
    @GetMapping("/category/{categoryId}")
    public Map<String, Object> getProductsByCategory(@PathVariable Long categoryId) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Product> products = productService.getProductsByCategory(categoryId);
            result.put("success", true);
            result.put("data", products);
            result.put("message", "获取分类商品成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取分类商品失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 搜索商品
     */
    @GetMapping("/search")
    public Map<String, Object> searchProducts(@RequestParam String name) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Product> products = productService.searchProductsByName(name);
            result.put("success", true);
            result.put("data", products);
            result.put("message", "搜索商品成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "搜索商品失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 添加商品
     */
    @PostMapping("/add")
    @Transactional
    public Map<String, Object> addProduct(@RequestBody Map<String, Object> productData) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 参数验证
            String name = (String) productData.get("name");
            if (name == null || name.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "商品名称不能为空");
                return result;
            }

            Long categoryId = Long.valueOf(productData.get("categoryId").toString());
            if (categoryId == null) {
                result.put("success", false);
                result.put("message", "商品分类不能为空");
                return result;
            }

            Double price = Double.valueOf(productData.get("price").toString());
            if (price == null || price <= 0) {
                result.put("success", false);
                result.put("message", "商品价格必须大于0");
                return result;
            }

            // 创建商品对象
            Product product = new Product();
            product.setName(name.trim());
            product.setCategoryId(categoryId);
            product.setPrice(new BigDecimal(price));
            product.setDescription((String) productData.get("description"));
            product.setImageUrl((String) productData.get("imageUrl"));
            product.setStatus(Integer.valueOf(productData.get("status").toString()));

            // 获取库存信息
            Integer quantity = productData.get("quantity") != null ?
                Integer.valueOf(productData.get("quantity").toString()) : 0;
            Integer minStock = productData.get("minStock") != null ?
                Integer.valueOf(productData.get("minStock").toString()) : 10;

            boolean success = productService.addProduct(product);
            if (success && product.getId() != null) {
                try {
                    // 检查库存记录是否已存在
                    Stock existingStock = stockService.getStockByProductId(product.getId());
                    if (existingStock == null) {
                        // 创建新的库存记录
                        Stock stock = new Stock();
                        stock.setProductId(product.getId());
                        stock.setQuantity(quantity);
                        stock.setMinStock(minStock);
                        boolean stockSaved = stockService.save(stock);
                        if (!stockSaved) {
                            throw new RuntimeException("库存记录创建失败");
                        }
                    } else {
                        // 更新现有库存记录
                        existingStock.setQuantity(quantity);
                        existingStock.setMinStock(minStock);
                        boolean stockUpdated = stockService.updateById(existingStock);
                        if (!stockUpdated) {
                            throw new RuntimeException("库存记录更新失败");
                        }
                    }

                    result.put("success", true);
                    result.put("message", "添加商品成功");
                    result.put("data", product);
                } catch (Exception stockException) {
                    // 库存操作失败，抛出异常触发事务回滚
                    throw new RuntimeException("库存记录处理失败：" + stockException.getMessage());
                }
            } else {
                result.put("success", false);
                result.put("message", "添加商品失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "添加商品失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 更新商品信息
     */
    @PutMapping("/update")
    public Map<String, Object> updateProduct(@RequestBody Map<String, Object> productData) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long productId = Long.valueOf(productData.get("id").toString());
            if (productId == null) {
                result.put("success", false);
                result.put("message", "商品ID不能为空");
                return result;
            }

            // 创建商品对象
            Product product = new Product();
            product.setId(productId);
            product.setName((String) productData.get("name"));
            product.setCategoryId(Long.valueOf(productData.get("categoryId").toString()));
            product.setPrice(new BigDecimal(productData.get("price").toString()));
            product.setDescription((String) productData.get("description"));
            product.setImageUrl((String) productData.get("imageUrl"));
            product.setStatus(Integer.valueOf(productData.get("status").toString()));

            boolean success = productService.updateProduct(product);
            if (success) {
                // 更新库存信息
                if (productData.containsKey("quantity") || productData.containsKey("minStock")) {
                    Stock existingStock = stockService.getStockByProductId(productId);
                    if (existingStock != null) {
                        if (productData.containsKey("quantity")) {
                            existingStock.setQuantity(Integer.valueOf(productData.get("quantity").toString()));
                        }
                        if (productData.containsKey("minStock")) {
                            existingStock.setMinStock(Integer.valueOf(productData.get("minStock").toString()));
                        }
                        stockService.updateById(existingStock);
                    } else {
                        // 如果库存记录不存在，创建新的
                        Stock newStock = new Stock();
                        newStock.setProductId(productId);
                        newStock.setQuantity(productData.containsKey("quantity") ?
                            Integer.valueOf(productData.get("quantity").toString()) : 0);
                        newStock.setMinStock(productData.containsKey("minStock") ?
                            Integer.valueOf(productData.get("minStock").toString()) : 10);
                        stockService.save(newStock);
                    }
                }

                result.put("success", true);
                result.put("message", "更新商品成功");
            } else {
                result.put("success", false);
                result.put("message", "更新商品失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新商品失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 删除商品
     */
    @DeleteMapping("/{productId}")
    public Map<String, Object> deleteProduct(@PathVariable Long productId) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = productService.deleteProduct(productId);
            if (success) {
                result.put("success", true);
                result.put("message", "删除商品成功");
            } else {
                result.put("success", false);
                result.put("message", "删除商品失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除商品失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 上架商品
     */
    @PutMapping("/online/{productId}")
    public Map<String, Object> onlineProduct(@PathVariable Long productId) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = productService.onlineProduct(productId);
            if (success) {
                result.put("success", true);
                result.put("message", "商品上架成功");
            } else {
                result.put("success", false);
                result.put("message", "商品上架失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "商品上架失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 下架商品
     */
    @PutMapping("/offline/{productId}")
    public Map<String, Object> offlineProduct(@PathVariable Long productId) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = productService.offlineProduct(productId);
            if (success) {
                result.put("success", true);
                result.put("message", "商品下架成功");
            } else {
                result.put("success", false);
                result.put("message", "商品下架失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "商品下架失败：" + e.getMessage());
        }
        return result;
    }
}
