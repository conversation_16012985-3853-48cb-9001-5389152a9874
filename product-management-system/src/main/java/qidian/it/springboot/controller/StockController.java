package qidian.it.springboot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import qidian.it.springboot.entity.Stock;
import qidian.it.springboot.service.StockService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 库存管理控制器
 */
@RestController
@RequestMapping("/api/stock")
public class StockController {

    @Autowired
    private StockService stockService;

    /**
     * 获取所有库存信息
     */
    @GetMapping("/list")
    public Map<String, Object> getAllStock() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Map<String, Object>> stockList = stockService.getAllStockWithProduct();
            result.put("success", true);
            result.put("data", stockList);
            result.put("message", "获取库存列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取库存列表失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取库存不足的商品
     */
    @GetMapping("/low-stock")
    public Map<String, Object> getLowStockProducts() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Map<String, Object>> lowStockProducts = stockService.getLowStockProducts();
            result.put("success", true);
            result.put("data", lowStockProducts);
            result.put("message", "获取库存不足商品成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取库存不足商品失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 根据商品ID获取库存
     */
    @GetMapping("/product/{productId}")
    public Map<String, Object> getStockByProductId(@PathVariable Long productId) {
        Map<String, Object> result = new HashMap<>();
        try {
            Stock stock = stockService.getStockByProductId(productId);
            if (stock != null) {
                result.put("success", true);
                result.put("data", stock);
                result.put("message", "获取商品库存成功");
            } else {
                result.put("success", false);
                result.put("message", "商品库存不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取商品库存失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 更新库存数量
     */
    @PutMapping("/update")
    public Map<String, Object> updateStock(@RequestBody Map<String, Object> stockData) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long productId = Long.valueOf(stockData.get("productId").toString());
            Integer quantity = Integer.valueOf(stockData.get("quantity").toString());
            
            if (quantity < 0) {
                result.put("success", false);
                result.put("message", "库存数量不能为负数");
                return result;
            }
            
            boolean success = stockService.updateStock(productId, quantity);
            if (success) {
                result.put("success", true);
                result.put("message", "更新库存成功");
            } else {
                result.put("success", false);
                result.put("message", "更新库存失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新库存失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 入库操作（增加库存）
     */
    @PostMapping("/increase")
    public Map<String, Object> increaseStock(@RequestBody Map<String, Object> stockData) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long productId = Long.valueOf(stockData.get("productId").toString());
            Integer amount = Integer.valueOf(stockData.get("amount").toString());
            
            if (amount <= 0) {
                result.put("success", false);
                result.put("message", "入库数量必须大于0");
                return result;
            }
            
            boolean success = stockService.increaseStock(productId, amount);
            if (success) {
                result.put("success", true);
                result.put("message", "入库成功");
            } else {
                result.put("success", false);
                result.put("message", "入库失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "入库失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 出库操作（减少库存）
     */
    @PostMapping("/reduce")
    public Map<String, Object> reduceStock(@RequestBody Map<String, Object> stockData) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long productId = Long.valueOf(stockData.get("productId").toString());
            Integer amount = Integer.valueOf(stockData.get("amount").toString());
            
            if (amount <= 0) {
                result.put("success", false);
                result.put("message", "出库数量必须大于0");
                return result;
            }
            
            boolean success = stockService.reduceStock(productId, amount);
            if (success) {
                result.put("success", true);
                result.put("message", "出库成功");
            } else {
                result.put("success", false);
                result.put("message", "出库失败，库存不足");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "出库失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 批量更新库存
     */
    @PostMapping("/batch-update")
    public Map<String, Object> batchUpdateStock(@RequestBody List<Map<String, Object>> stockList) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (stockList == null || stockList.isEmpty()) {
                result.put("success", false);
                result.put("message", "库存数据不能为空");
                return result;
            }
            
            boolean success = stockService.batchUpdateStock(stockList);
            if (success) {
                result.put("success", true);
                result.put("message", "批量更新库存成功");
            } else {
                result.put("success", false);
                result.put("message", "批量更新库存失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "批量更新库存失败：" + e.getMessage());
        }
        return result;
    }
}
