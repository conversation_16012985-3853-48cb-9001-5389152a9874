package qidian.it.springboot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import qidian.it.springboot.entity.Cart;
import qidian.it.springboot.service.CartService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 购物车管理控制器
 */
@RestController
@RequestMapping("/api/cart")
public class CartController {

    @Autowired
    private CartService cartService;

    /**
     * 获取用户购物车
     */
    @GetMapping("/list/{userId}")
    public Map<String, Object> getCartList(@PathVariable Long userId) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Map<String, Object>> cartItems = cartService.getCartWithProductByUserId(userId);
            result.put("success", true);
            result.put("data", cartItems);
            result.put("message", "获取购物车成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取购物车失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 添加商品到购物车
     */
    @PostMapping("/add")
    public Map<String, Object> addToCart(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long userId = Long.valueOf(requestData.get("userId").toString());
            Long productId = Long.valueOf(requestData.get("productId").toString());
            Integer quantity = Integer.valueOf(requestData.get("quantity").toString());

            if (quantity <= 0) {
                result.put("success", false);
                result.put("message", "商品数量必须大于0");
                return result;
            }

            boolean success = cartService.addToCart(userId, productId, quantity);
            if (success) {
                result.put("success", true);
                result.put("message", "添加到购物车成功");
                
                // 返回购物车统计信息
                Integer itemCount = cartService.getCartItemCount(userId);
                Integer totalQuantity = cartService.getCartTotalQuantity(userId);
                Map<String, Object> cartInfo = new HashMap<>();
                cartInfo.put("itemCount", itemCount);
                cartInfo.put("totalQuantity", totalQuantity);
                result.put("cartInfo", cartInfo);
            } else {
                result.put("success", false);
                result.put("message", "添加到购物车失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "添加到购物车失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 更新购物车商品数量
     */
    @PutMapping("/update")
    public Map<String, Object> updateCartQuantity(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long cartId = Long.valueOf(requestData.get("cartId").toString());
            Integer quantity = Integer.valueOf(requestData.get("quantity").toString());

            if (quantity <= 0) {
                result.put("success", false);
                result.put("message", "商品数量必须大于0");
                return result;
            }

            boolean success = cartService.updateCartQuantity(cartId, quantity);
            if (success) {
                result.put("success", true);
                result.put("message", "更新数量成功");
            } else {
                result.put("success", false);
                result.put("message", "更新数量失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新数量失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 从购物车删除商品
     */
    @DeleteMapping("/remove")
    public Map<String, Object> removeFromCart(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long userId = Long.valueOf(requestData.get("userId").toString());
            Long productId = Long.valueOf(requestData.get("productId").toString());

            boolean success = cartService.removeFromCart(userId, productId);
            if (success) {
                result.put("success", true);
                result.put("message", "删除成功");
                
                // 返回购物车统计信息
                Integer itemCount = cartService.getCartItemCount(userId);
                Integer totalQuantity = cartService.getCartTotalQuantity(userId);
                Map<String, Object> cartInfo = new HashMap<>();
                cartInfo.put("itemCount", itemCount);
                cartInfo.put("totalQuantity", totalQuantity);
                result.put("cartInfo", cartInfo);
            } else {
                result.put("success", false);
                result.put("message", "删除失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 批量删除购物车商品
     */
    @DeleteMapping("/batch-remove")
    public Map<String, Object> batchRemoveFromCart(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long userId = Long.valueOf(requestData.get("userId").toString());
            @SuppressWarnings("unchecked")
            List<Long> productIds = (List<Long>) requestData.get("productIds");

            boolean success = cartService.batchRemoveFromCart(userId, productIds);
            if (success) {
                result.put("success", true);
                result.put("message", "批量删除成功");
                
                // 返回购物车统计信息
                Integer itemCount = cartService.getCartItemCount(userId);
                Integer totalQuantity = cartService.getCartTotalQuantity(userId);
                Map<String, Object> cartInfo = new HashMap<>();
                cartInfo.put("itemCount", itemCount);
                cartInfo.put("totalQuantity", totalQuantity);
                result.put("cartInfo", cartInfo);
            } else {
                result.put("success", false);
                result.put("message", "批量删除失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "批量删除失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 清空购物车
     */
    @DeleteMapping("/clear/{userId}")
    public Map<String, Object> clearCart(@PathVariable Long userId) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = cartService.clearCart(userId);
            if (success) {
                result.put("success", true);
                result.put("message", "清空购物车成功");
            } else {
                result.put("success", false);
                result.put("message", "清空购物车失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "清空购物车失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取购物车统计信息
     */
    @GetMapping("/info/{userId}")
    public Map<String, Object> getCartInfo(@PathVariable Long userId) {
        Map<String, Object> result = new HashMap<>();
        try {
            Integer itemCount = cartService.getCartItemCount(userId);
            Integer totalQuantity = cartService.getCartTotalQuantity(userId);
            
            Map<String, Object> cartInfo = new HashMap<>();
            cartInfo.put("itemCount", itemCount);
            cartInfo.put("totalQuantity", totalQuantity);
            
            result.put("success", true);
            result.put("data", cartInfo);
            result.put("message", "获取购物车信息成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取购物车信息失败：" + e.getMessage());
        }
        return result;
    }
}
