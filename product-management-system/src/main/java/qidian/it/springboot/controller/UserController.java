package qidian.it.springboot.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import qidian.it.springboot.entity.User;
import qidian.it.springboot.service.UserService;
import qidian.it.springboot.util.MD5Util;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/api/user")
public class                 UserController {

    @Autowired
    private UserService userService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Map<String, Object> register(@RequestBody User user) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查用户名是否已存在
            QueryWrapper<User> usernameWrapper = new QueryWrapper<>();
            usernameWrapper.eq("username", user.getUsername());
            User existingUserByUsername = userService.getOne(usernameWrapper);
            
            if (existingUserByUsername != null) {
                result.put("success", false);
                result.put("message", "用户名已存在");
                return result;
            }
            
            // 检查邮箱是否已存在
            QueryWrapper<User> emailWrapper = new QueryWrapper<>();
            emailWrapper.eq("email", user.getEmail());
            User existingUserByEmail = userService.getOne(emailWrapper);
            
            if (existingUserByEmail != null) {
                result.put("success", false);
                result.put("message", "邮箱已被注册");
                return result;
            }
            
            // 密码MD5加密
            String encryptedPassword = MD5Util.MD5PassWord(user.getPassword());
            user.setPassword(encryptedPassword);
            
            // 设置默认状态为正常
            user.setStatus(1);
            
            // 保存用户
            boolean saved = userService.save(user);
            
            if (saved) {
                result.put("success", true);
                result.put("message", "注册成功");
                result.put("data", user.getId());
            } else {
                result.put("success", false);
                result.put("message", "注册失败，请重试");
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "注册失败：" + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody Map<String, String> loginData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String username = loginData.get("username");
            String password = loginData.get("password");
            
            if (username == null || username.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "用户名不能为空");
                return result;
            }
            
            if (password == null || password.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "密码不能为空");
                return result;
            }
            
            // 根据用户名或邮箱查找用户
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.and(w -> w.eq("username", username).or().eq("email", username));
            User user = userService.getOne(wrapper);
            
            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }
            
            // 检查用户状态
            if (user.getStatus() == 0) {
                result.put("success", false);
                result.put("message", "账户已被禁用，请联系管理员");
                return result;
            }
            
            // 验证密码
            boolean passwordValid = MD5Util.checkPassword(password, user.getPassword());
            
            if (passwordValid) {
                result.put("success", true);
                result.put("message", "登录成功");
                
                // 返回用户信息（不包含密码）
                Map<String, Object> userData = new HashMap<>();
                userData.put("id", user.getId());
                userData.put("username", user.getUsername());
                userData.put("email", user.getEmail());
                userData.put("phone", user.getPhone());
                userData.put("createTime", user.getCreateTime());
                userData.put("status", user.getStatus());
                
                result.put("data", userData);
            } else {
                result.put("success", false);
                result.put("message", "密码错误");
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "登录失败：" + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    public Map<String, Object> checkUsername(@RequestParam String username) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.eq("username", username);
            User existingUser = userService.getOne(wrapper);
            
            result.put("success", true);
            result.put("available", existingUser == null);
            result.put("message", existingUser == null ? "用户名可用" : "用户名已存在");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "检查失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    public Map<String, Object> checkEmail(@RequestParam String email) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.eq("email", email);
            User existingUser = userService.getOne(wrapper);
            
            result.put("success", true);
            result.put("available", existingUser == null);
            result.put("message", existingUser == null ? "邮箱可用" : "邮箱已被注册");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "检查失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/profile/{userId}")
    public Map<String, Object> getUserProfile(@PathVariable Long userId) {
        Map<String, Object> result = new HashMap<>();

        try {
            User user = userService.getById(userId);

            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }

            // 返回用户信息（不包含密码）
            Map<String, Object> userData = new HashMap<>();
            userData.put("id", user.getId());
            userData.put("username", user.getUsername());
            userData.put("email", user.getEmail());
            userData.put("phone", user.getPhone());
            userData.put("createTime", user.getCreateTime());
            userData.put("status", user.getStatus());

            result.put("success", true);
            result.put("message", "获取用户信息成功");
            result.put("data", userData);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取用户信息失败：" + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/profile/{userId}")
    public Map<String, Object> updateUserProfile(@PathVariable Long userId, @RequestBody Map<String, Object> updateData) {
        Map<String, Object> result = new HashMap<>();

        try {
            User user = userService.getById(userId);

            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }

            // 更新用户信息
            if (updateData.containsKey("email")) {
                String newEmail = (String) updateData.get("email");
                // 检查新邮箱是否已被其他用户使用
                QueryWrapper<User> emailWrapper = new QueryWrapper<>();
                emailWrapper.eq("email", newEmail).ne("id", userId);
                User existingUser = userService.getOne(emailWrapper);

                if (existingUser != null) {
                    result.put("success", false);
                    result.put("message", "邮箱已被其他用户使用");
                    return result;
                }
                user.setEmail(newEmail);
            }

            if (updateData.containsKey("phone")) {
                user.setPhone((String) updateData.get("phone"));
            }

            // 保存更新
            boolean updated = userService.updateById(user);

            if (updated) {
                result.put("success", true);
                result.put("message", "更新成功");

                // 返回更新后的用户信息
                Map<String, Object> userData = new HashMap<>();
                userData.put("id", user.getId());
                userData.put("username", user.getUsername());
                userData.put("email", user.getEmail());
                userData.put("phone", user.getPhone());
                userData.put("createTime", user.getCreateTime());
                userData.put("status", user.getStatus());

                result.put("data", userData);
            } else {
                result.put("success", false);
                result.put("message", "更新失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 修改密码
     */
    @PutMapping("/change-password/{userId}")
    public Map<String, Object> changePassword(@PathVariable Long userId, @RequestBody Map<String, String> passwordData) {
        Map<String, Object> result = new HashMap<>();

        try {
            String oldPassword = passwordData.get("oldPassword");
            String newPassword = passwordData.get("newPassword");

            if (oldPassword == null || oldPassword.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "原密码不能为空");
                return result;
            }

            if (newPassword == null || newPassword.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "新密码不能为空");
                return result;
            }

            if (newPassword.length() < 6) {
                result.put("success", false);
                result.put("message", "新密码长度不能少于6位");
                return result;
            }

            User user = userService.getById(userId);

            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }

            // 验证原密码
            boolean oldPasswordValid = MD5Util.checkPassword(oldPassword, user.getPassword());

            if (!oldPasswordValid) {
                result.put("success", false);
                result.put("message", "原密码错误");
                return result;
            }

            // 加密新密码
            String encryptedNewPassword = MD5Util.MD5PassWord(newPassword);
            user.setPassword(encryptedNewPassword);

            // 保存更新
            boolean updated = userService.updateById(user);

            if (updated) {
                result.put("success", true);
                result.put("message", "密码修改成功");
            } else {
                result.put("success", false);
                result.put("message", "密码修改失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "密码修改失败：" + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }
}
