package qidian.it.springboot.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import qidian.it.springboot.entity.User;
import qidian.it.springboot.service.EmailService;
import qidian.it.springboot.service.UserService;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 忘记密码控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/forgot-password")
public class ForgotPasswordController {
    
    @Autowired
    private EmailService emailService;
    
    @Autowired
    private UserService userService;
    
    // 邮箱格式验证正则表达式
    private static final String EMAIL_PATTERN = 
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
    
    private static final Pattern pattern = Pattern.compile(EMAIL_PATTERN);
    
    /**
     * 发送忘记密码验证码
     */
    @PostMapping("/send-code")
    public Map<String, Object> sendCode(@RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String username = request.get("username");

            // 验证用户名
            if (username == null || username.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "用户名不能为空");
                return result;
            }

            // 检查用户名是否存在
            User user = userService.getUserByUsername(username);
            if (user == null) {
                result.put("success", false);
                result.put("message", "该用户名不存在");
                return result;
            }

            // 检查用户是否绑定了邮箱
            if (user.getEmail() == null || user.getEmail().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "该用户未绑定邮箱，无法找回密码");
                return result;
            }

            // 发送验证码到用户邮箱
            boolean sent = emailService.sendForgotPasswordCode(user.getEmail());
            if (sent) {
                result.put("success", true);
                result.put("message", "验证码已发送到您的邮箱，请查收");
                // 返回脱敏的邮箱地址供用户确认
                result.put("email", maskEmail(user.getEmail()));
            } else {
                result.put("success", false);
                result.put("message", "验证码发送失败，请稍后重试");
            }

        } catch (Exception e) {
            log.error("发送忘记密码验证码失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "系统错误，请稍后重试");
        }

        return result;
    }
    
    /**
     * 验证验证码并重置密码
     */
    @PostMapping("/reset-password")
    public Map<String, Object> resetPassword(@RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String username = request.get("username");
            String code = request.get("code");
            String newPassword = request.get("newPassword");

            // 参数验证
            if (username == null || username.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "用户名不能为空");
                return result;
            }

            if (code == null || code.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "验证码不能为空");
                return result;
            }

            if (newPassword == null || newPassword.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "新密码不能为空");
                return result;
            }

            if (newPassword.length() < 6) {
                result.put("success", false);
                result.put("message", "密码长度不能少于6位");
                return result;
            }

            // 根据用户名获取用户信息
            User user = userService.getUserByUsername(username);
            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }

            // 验证验证码（使用用户的邮箱）
            boolean codeValid = emailService.verifyForgotPasswordCode(user.getEmail(), code);
            if (!codeValid) {
                result.put("success", false);
                result.put("message", "验证码错误或已过期");
                return result;
            }

            // 重置密码
            boolean resetSuccess = userService.resetPasswordByUsername(username, newPassword);
            if (resetSuccess) {
                result.put("success", true);
                result.put("message", "密码重置成功，请使用新密码登录");
            } else {
                result.put("success", false);
                result.put("message", "密码重置失败，请稍后重试");
            }

        } catch (Exception e) {
            log.error("重置密码失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "系统错误，请稍后重试");
        }

        return result;
    }
    
    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        return pattern.matcher(email).matches();
    }

    /**
     * 邮箱脱敏处理
     */
    private String maskEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return "";
        }

        int atIndex = email.indexOf("@");
        if (atIndex <= 0) {
            return email;
        }

        String username = email.substring(0, atIndex);
        String domain = email.substring(atIndex);

        if (username.length() <= 2) {
            return username + "***" + domain;
        } else if (username.length() <= 4) {
            return username.substring(0, 1) + "***" + username.substring(username.length() - 1) + domain;
        } else {
            return username.substring(0, 2) + "***" + username.substring(username.length() - 2) + domain;
        }
    }
}
