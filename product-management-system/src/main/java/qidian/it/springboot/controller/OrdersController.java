package qidian.it.springboot.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import qidian.it.springboot.entity.Orders;
import qidian.it.springboot.service.OrdersService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单管理控制器
 */
@RestController
@RequestMapping("/api/orders")
public class OrdersController {

    @Autowired
    private OrdersService ordersService;

    /**
     * 从购物车创建订单
     */
    @PostMapping("/create-from-cart")
    public Map<String, Object> createOrderFromCart(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long userId = Long.valueOf(requestData.get("userId").toString());
            @SuppressWarnings("unchecked")
            List<Object> cartItemIdsRaw = (List<Object>) requestData.get("cartItemIds");

            // 转换为Long类型
            List<Long> cartItemIds = new ArrayList<>();
            for (Object id : cartItemIdsRaw) {
                cartItemIds.add(Long.valueOf(id.toString()));
            }

            Orders order = ordersService.createOrderFromCart(userId, cartItemIds);
            if (order != null) {
                result.put("success", true);
                result.put("data", order);
                result.put("message", "订单创建成功");
            } else {
                result.put("success", false);
                result.put("message", "订单创建失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "订单创建失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 直接购买创建订单
     */
    @PostMapping("/create-direct")
    public Map<String, Object> createOrderDirect(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long userId = Long.valueOf(requestData.get("userId").toString());
            Long productId = Long.valueOf(requestData.get("productId").toString());
            Integer quantity = Integer.valueOf(requestData.get("quantity").toString());

            Orders order = ordersService.createOrderDirect(userId, productId, quantity);
            if (order != null) {
                result.put("success", true);
                result.put("data", order);
                result.put("message", "订单创建成功");
            } else {
                result.put("success", false);
                result.put("message", "订单创建失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "订单创建失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取用户订单列表
     */
    @GetMapping("/list/{userId}")
    public Map<String, Object> getUserOrders(@PathVariable Long userId) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Map<String, Object>> orders = ordersService.getUserOrders(userId);
            result.put("success", true);
            result.put("data", orders);
            result.put("message", "获取订单列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取订单列表失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/detail/{orderId}")
    public Map<String, Object> getOrderDetail(@PathVariable Long orderId) {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> orderDetail = ordersService.getOrderDetail(orderId);
            if (orderDetail != null) {
                result.put("success", true);
                result.put("data", orderDetail);
                result.put("message", "获取订单详情成功");
            } else {
                result.put("success", false);
                result.put("message", "订单不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取订单详情失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 取消订单
     */
    @PutMapping("/cancel/{orderId}")
    public Map<String, Object> cancelOrder(@PathVariable Long orderId, @RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long userId = Long.valueOf(requestData.get("userId").toString());
            boolean success = ordersService.cancelOrder(orderId, userId);
            if (success) {
                result.put("success", true);
                result.put("message", "订单取消成功");
            } else {
                result.put("success", false);
                result.put("message", "订单取消失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "订单取消失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 确认收货
     */
    @PutMapping("/confirm/{orderId}")
    public Map<String, Object> confirmOrder(@PathVariable Long orderId, @RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long userId = Long.valueOf(requestData.get("userId").toString());
            boolean success = ordersService.confirmOrder(orderId, userId);
            if (success) {
                result.put("success", true);
                result.put("message", "确认收货成功");
            } else {
                result.put("success", false);
                result.put("message", "确认收货失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "确认收货失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取订单统计信息
     */
    @GetMapping("/statistics/{userId}")
    public Map<String, Object> getOrderStatistics(@PathVariable Long userId) {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> statistics = ordersService.getOrderStatistics(userId);
            result.put("success", true);
            result.put("data", statistics);
            result.put("message", "获取订单统计成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取订单统计失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 根据订单号查询订单
     */
    @GetMapping("/order-no/{orderNo}")
    public Map<String, Object> getOrderByOrderNo(@PathVariable String orderNo) {
        Map<String, Object> result = new HashMap<>();
        try {
            Orders order = ordersService.getOrderByOrderNo(orderNo);
            if (order != null) {
                result.put("success", true);
                result.put("data", order);
                result.put("message", "查询订单成功");
            } else {
                result.put("success", false);
                result.put("message", "订单不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询订单失败：" + e.getMessage());
        }
        return result;
    }

    // ========================================
    // 管理员订单管理接口
    // ========================================

    /**
     * 获取所有订单列表（管理员）
     */
    @GetMapping("/admin/list")
    public Map<String, Object> getAllOrdersForAdmin() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Map<String, Object>> orders = ordersService.getAllOrdersForAdmin();
            result.put("success", true);
            result.put("data", orders);
            result.put("message", "获取订单列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取订单列表失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 更新订单状态（管理员）
     */
    @PutMapping("/admin/status/{orderId}")
    public Map<String, Object> updateOrderStatus(@PathVariable Long orderId, @RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        try {
            Integer status = Integer.valueOf(requestData.get("status").toString());
            String remark = requestData.get("remark") != null ? requestData.get("remark").toString() : "";

            boolean success = ordersService.updateOrderStatusByAdmin(orderId, status, remark);
            if (success) {
                result.put("success", true);
                result.put("message", "订单状态更新成功");
            } else {
                result.put("success", false);
                result.put("message", "订单状态更新失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "订单状态更新失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取订单统计信息（管理员）
     */
    @GetMapping("/admin/statistics")
    public Map<String, Object> getAdminOrderStatistics() {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> statistics = ordersService.getAdminOrderStatistics();
            result.put("success", true);
            result.put("data", statistics);
            result.put("message", "获取订单统计成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取订单统计失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 搜索订单列表（管理员）
     */
    @GetMapping("/admin/search")
    public Map<String, Object> searchOrdersForAdmin(
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "status", required = false) Integer status) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Map<String, Object>> orders = ordersService.searchOrdersForAdmin(keyword, status);
            result.put("success", true);
            result.put("data", orders);
            result.put("message", "搜索订单成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "搜索订单失败：" + e.getMessage());
        }
        return result;
    }
}
