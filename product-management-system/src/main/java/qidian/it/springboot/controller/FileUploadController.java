package qidian.it.springboot.controller;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 文件上传控制器
 */
@RestController
@RequestMapping("/api/upload")
public class FileUploadController {

    // 图片存储路径
    private static final String UPLOAD_DIR = "D:/product_image/";
    
    // 允许的图片格式
    private static final String[] ALLOWED_EXTENSIONS = {".jpg", ".jpeg", ".png", ".gif", ".bmp"};
    
    // 最大文件大小 (5MB)
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024;

    /**
     * 上传商品图片
     */
    @PostMapping("/product-image")
    public Map<String, Object> uploadProductImage(@RequestParam("file") MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证文件是否为空
            if (file.isEmpty()) {
                result.put("success", false);
                result.put("message", "请选择要上传的文件");
                return result;
            }
            
            // 验证文件大小
            if (file.getSize() > MAX_FILE_SIZE) {
                result.put("success", false);
                result.put("message", "文件大小不能超过5MB");
                return result;
            }
            
            // 获取原始文件名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.isEmpty()) {
                result.put("success", false);
                result.put("message", "文件名不能为空");
                return result;
            }
            
            // 验证文件格式
            String fileExtension = getFileExtension(originalFilename);
            if (!isAllowedExtension(fileExtension)) {
                result.put("success", false);
                result.put("message", "只支持 jpg、jpeg、png、gif、bmp 格式的图片");
                return result;
            }
            
            // 创建上传目录
            File uploadDir = new File(UPLOAD_DIR);
            if (!uploadDir.exists()) {
                boolean created = uploadDir.mkdirs();
                if (!created) {
                    result.put("success", false);
                    result.put("message", "创建上传目录失败");
                    return result;
                }
            }
            
            // 生成唯一文件名
            String fileName = generateUniqueFileName(fileExtension);
            
            // 保存文件
            Path filePath = Paths.get(UPLOAD_DIR + fileName);
            Files.copy(file.getInputStream(), filePath);
            
            // 生成访问URL
            String imageUrl = "/product_image/" + fileName;
            
            System.out.println("图片上传成功:");
            System.out.println("原始文件名: " + originalFilename);
            System.out.println("保存文件名: " + fileName);
            System.out.println("访问URL: " + imageUrl);
            
            result.put("success", true);
            result.put("message", "图片上传成功");
            result.put("data", Map.of(
                "fileName", fileName,
                "originalName", originalFilename,
                "imageUrl", imageUrl,
                "fileSize", file.getSize()
            ));
            
        } catch (IOException e) {
            System.err.println("文件上传失败: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "文件上传失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("上传过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "上传失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 删除商品图片
     */
    @DeleteMapping("/product-image")
    public Map<String, Object> deleteProductImage(@RequestParam("fileName") String fileName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (fileName == null || fileName.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "文件名不能为空");
                return result;
            }
            
            // 安全检查：防止路径遍历攻击
            if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
                result.put("success", false);
                result.put("message", "文件名包含非法字符");
                return result;
            }
            
            Path filePath = Paths.get(UPLOAD_DIR + fileName);
            
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                System.out.println("图片删除成功: " + fileName);
                result.put("success", true);
                result.put("message", "图片删除成功");
            } else {
                result.put("success", false);
                result.put("message", "文件不存在");
            }
            
        } catch (IOException e) {
            System.err.println("文件删除失败: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "文件删除失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("删除过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "删除失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return fileName.substring(lastDotIndex).toLowerCase();
    }
    
    /**
     * 检查文件扩展名是否被允许
     */
    private boolean isAllowedExtension(String extension) {
        for (String allowedExt : ALLOWED_EXTENSIONS) {
            if (allowedExt.equals(extension)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 生成唯一文件名
     */
    private String generateUniqueFileName(String extension) {
        return UUID.randomUUID().toString().replace("-", "") + extension;
    }
}
