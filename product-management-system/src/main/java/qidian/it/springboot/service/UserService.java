package qidian.it.springboot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import qidian.it.springboot.entity.User;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User> {

    /**
     * 根据邮箱查询用户
     */
    User getUserByEmail(String email);

    /**
     * 根据用户名查询用户
     */
    User getUserByUsername(String username);

    /**
     * 根据邮箱重置密码
     */
    boolean resetPasswordByEmail(String email, String newPassword);

    /**
     * 根据用户名重置密码
     */
    boolean resetPasswordByUsername(String username, String newPassword);

}
