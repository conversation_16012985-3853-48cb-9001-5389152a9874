package qidian.it.springboot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import qidian.it.springboot.entity.Stock;

import java.util.List;
import java.util.Map;

/**
 * 库存服务接口
 */
public interface StockService extends IService<Stock> {

    /**
     * 根据商品ID查询库存
     */
    Stock getStockByProductId(Long productId);

    /**
     * 查询库存不足的商品
     */
    List<Map<String, Object>> getLowStockProducts();

    /**
     * 查询所有库存信息
     */
    List<Map<String, Object>> getAllStockWithProduct();

    /**
     * 更新库存数量
     */
    boolean updateStock(Long productId, Integer quantity);

    /**
     * 减少库存（用于下单）
     */
    boolean reduceStock(Long productId, Integer amount);

    /**
     * 增加库存（用于退货或入库）
     */
    boolean increaseStock(Long productId, Integer amount);

    /**
     * 批量更新库存
     */
    boolean batchUpdateStock(List<Map<String, Object>> stockList);

    /**
     * 检查库存是否足够
     */
    boolean checkStockAvailable(Long productId, Integer requiredQuantity);

    /**
     * 批量检查库存是否足够
     */
    boolean batchCheckStockAvailable(List<Map<String, Object>> items);

    /**
     * 预扣库存（下单时）
     */
    boolean reserveStock(Long productId, Integer amount);

    /**
     * 释放预扣库存（取消订单时）
     */
    boolean releaseStock(Long productId, Integer amount);

    /**
     * 确认扣减库存（支付成功时）
     */
    boolean confirmReduceStock(Long productId, Integer amount);

    /**
     * 批量扣减库存（支付成功时）
     */
    boolean batchReduceStock(List<Map<String, Object>> items);
}
