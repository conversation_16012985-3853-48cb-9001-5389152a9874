package qidian.it.springboot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import qidian.it.springboot.entity.Category;

import java.util.List;

/**
 * 商品分类服务接口
 */
public interface CategoryService extends IService<Category> {
    
    /**
     * 查询所有分类
     */
    List<Category> getAllCategories();
    
    /**
     * 根据分类名称查询分类
     */
    Category getCategoryByName(String name);
    
    /**
     * 查询分类下的商品数量
     */
    Integer getProductCountByCategory(Long categoryId);
    
    /**
     * 添加分类
     */
    boolean addCategory(Category category);
    
    /**
     * 更新分类
     */
    boolean updateCategory(Category category);
    
    /**
     * 删除分类（检查是否有商品）
     */
    boolean deleteCategory(Long categoryId);
}
