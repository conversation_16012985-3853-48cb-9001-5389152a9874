package qidian.it.springboot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qidian.it.springboot.entity.User;
import qidian.it.springboot.mapper.UserMapper;
import qidian.it.springboot.service.UserService;
import qidian.it.springboot.util.MD5Util;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public User getUserByEmail(String email) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("email", email);
        return userMapper.selectOne(wrapper);
    }

    @Override
    public User getUserByUsername(String username) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("username", username);
        return userMapper.selectOne(wrapper);
    }

    @Override
    public boolean resetPasswordByEmail(String email, String newPassword) {
        try {
            // 使用MD5加密新密码，保持与登录功能一致
            String encodedPassword = MD5Util.MD5PassWord(newPassword);

            // 更新密码
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.eq("email", email);

            User user = new User();
            user.setPassword(encodedPassword);

            int result = userMapper.update(user, wrapper);
            return result > 0;

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean resetPasswordByUsername(String username, String newPassword) {
        try {
            // 使用MD5加密新密码，保持与登录功能一致
            String encodedPassword = MD5Util.MD5PassWord(newPassword);

            // 更新密码
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.eq("username", username);

            User user = new User();
            user.setPassword(encodedPassword);

            int result = userMapper.update(user, wrapper);
            return result > 0;

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
