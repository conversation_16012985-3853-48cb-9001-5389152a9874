package qidian.it.springboot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import qidian.it.springboot.entity.Product;

import java.util.List;
import java.util.Map;

/**
 * 商品服务接口
 */
public interface ProductService extends IService<Product> {
    
    /**
     * 查询所有商品及其分类信息
     */
    List<Map<String, Object>> getProductsWithCategory();
    
    /**
     * 根据分类ID查询商品
     */
    List<Product> getProductsByCategory(Long categoryId);
    
    /**
     * 查询上架商品
     */
    List<Product> getOnlineProducts();

    /**
     * 查询上架商品及其分类信息
     */
    List<Map<String, Object>> getOnlineProductsWithCategory();
    
    /**
     * 根据商品名称模糊查询
     */
    List<Product> searchProductsByName(String name);
    
    /**
     * 查询商品及其库存信息
     */
    List<Map<String, Object>> getProductsWithStock();

    /**
     * 根据商品ID查询商品及其库存信息
     */
    Map<String, Object> getProductWithStockById(Long productId);
    
    /**
     * 添加商品
     */
    boolean addProduct(Product product);
    
    /**
     * 更新商品信息
     */
    boolean updateProduct(Product product);
    
    /**
     * 删除商品（同时删除库存信息）
     */
    boolean deleteProduct(Long productId);
    
    /**
     * 上架商品
     */
    boolean onlineProduct(Long productId);
    
    /**
     * 下架商品
     */
    boolean offlineProduct(Long productId);
}
