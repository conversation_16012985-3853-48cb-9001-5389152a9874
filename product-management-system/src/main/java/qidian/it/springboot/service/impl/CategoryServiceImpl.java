package qidian.it.springboot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qidian.it.springboot.entity.Category;
import qidian.it.springboot.mapper.CategoryMapper;
import qidian.it.springboot.service.CategoryService;

import java.util.List;

/**
 * 商品分类服务实现类
 */
@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements CategoryService {
    
    @Autowired
    private CategoryMapper categoryMapper;
    
    @Override
    public List<Category> getAllCategories() {
        return categoryMapper.selectAllCategories();
    }
    
    @Override
    public Category getCategoryByName(String name) {
        return categoryMapper.selectByName(name);
    }
    
    @Override
    public Integer getProductCountByCategory(Long categoryId) {
        return categoryMapper.countProductsByCategory(categoryId);
    }
    
    @Override
    public boolean addCategory(Category category) {
        // 检查分类名称是否已存在
        Category existingCategory = getCategoryByName(category.getName());
        if (existingCategory != null) {
            return false; // 分类名称已存在
        }
        
        return save(category);
    }
    
    @Override
    public boolean updateCategory(Category category) {
        // 检查分类名称是否已被其他分类使用
        Category existingCategory = getCategoryByName(category.getName());
        if (existingCategory != null && !existingCategory.getId().equals(category.getId())) {
            return false; // 分类名称已被其他分类使用
        }
        
        return updateById(category);
    }
    
    @Override
    public boolean deleteCategory(Long categoryId) {
        // 检查该分类下是否有商品
        Integer productCount = getProductCountByCategory(categoryId);
        if (productCount > 0) {
            return false; // 该分类下还有商品，不能删除
        }
        
        return removeById(categoryId);
    }
}
