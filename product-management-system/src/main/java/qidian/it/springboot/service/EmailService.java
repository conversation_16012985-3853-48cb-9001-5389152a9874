package qidian.it.springboot.service;

/**
 * 邮件服务接口
 */
public interface EmailService {
    
    /**
     * 发送忘记密码验证码
     * @param email 邮箱地址
     * @return 是否发送成功
     */
    boolean sendForgotPasswordCode(String email);
    
    /**
     * 验证忘记密码验证码
     * @param email 邮箱地址
     * @param code 验证码
     * @return 是否验证成功
     */
    boolean verifyForgotPasswordCode(String email, String code);
    
    /**
     * 发送注册验证码
     * @param email 邮箱地址
     * @return 是否发送成功
     */
    boolean sendRegisterCode(String email);
    
    /**
     * 验证注册验证码
     * @param email 邮箱地址
     * @param code 验证码
     * @return 是否验证成功
     */
    boolean verifyRegisterCode(String email, String code);
    
    /**
     * 清理过期验证码
     */
    void cleanExpiredCodes();
}
