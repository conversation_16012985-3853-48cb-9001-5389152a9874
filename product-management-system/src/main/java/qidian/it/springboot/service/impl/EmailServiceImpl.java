package qidian.it.springboot.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import qidian.it.springboot.entity.EmailVerification;
import qidian.it.springboot.mapper.EmailVerificationMapper;
import qidian.it.springboot.service.EmailService;

import javax.mail.internet.MimeMessage;
import java.security.SecureRandom;
import java.time.LocalDateTime;

/**
 * 邮件服务实现类
 */
@Slf4j
@Service
public class EmailServiceImpl implements EmailService {
    
    @Autowired
    private JavaMailSender mailSender;
    
    @Autowired
    private EmailVerificationMapper emailVerificationMapper;
    
    @Value("${spring.mail.username}")
    private String fromEmail;
    
    private static final int CODE_LENGTH = 6;
    private static final int CODE_EXPIRE_MINUTES = 10;
    private static final int SEND_INTERVAL_MINUTES = 1;
    
    @Override
    public boolean sendForgotPasswordCode(String email) {
        return sendVerificationCode(email, EmailVerification.Type.FORGOT_PASSWORD, "密码重置验证码");
    }
    
    @Override
    public boolean verifyForgotPasswordCode(String email, String code) {
        return verifyCode(email, code, EmailVerification.Type.FORGOT_PASSWORD);
    }
    
    @Override
    public boolean sendRegisterCode(String email) {
        return sendVerificationCode(email, EmailVerification.Type.REGISTER, "注册验证码");
    }
    
    @Override
    public boolean verifyRegisterCode(String email, String code) {
        return verifyCode(email, code, EmailVerification.Type.REGISTER);
    }
    
    /**
     * 发送验证码
     */
    private boolean sendVerificationCode(String email, int type, String subject) {
        try {
            // 检查发送频率限制
            LocalDateTime since = LocalDateTime.now().minusMinutes(SEND_INTERVAL_MINUTES);
            int recentCount = emailVerificationMapper.countRecentCodes(email, type, since);
            if (recentCount > 0) {
                log.warn("邮箱 {} 发送验证码过于频繁", email);
                return false;
            }
            
            // 生成验证码
            String code = generateCode();
            
            // 发送邮件
            boolean sent = sendEmail(email, subject, buildEmailContent(code, subject));
            if (!sent) {
                return false;
            }
            
            // 保存验证码到数据库
            EmailVerification verification = new EmailVerification();
            verification.setEmail(email);
            verification.setCode(code);
            verification.setType(type);
            verification.setExpireTime(LocalDateTime.now().plusMinutes(CODE_EXPIRE_MINUTES));
            verification.setUsed(EmailVerification.Status.UNUSED);
            verification.setCreateTime(LocalDateTime.now());
            
            int result = emailVerificationMapper.insert(verification);
            
            log.info("验证码发送成功，邮箱：{}，类型：{}", email, type);
            return result > 0;
            
        } catch (Exception e) {
            log.error("发送验证码失败，邮箱：{}，错误：{}", email, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 验证验证码
     */
    private boolean verifyCode(String email, String code, int type) {
        try {
            // 查询有效的验证码
            EmailVerification verification = emailVerificationMapper.selectValidCode(
                email, type, LocalDateTime.now());
            
            if (verification == null) {
                log.warn("验证码不存在或已过期，邮箱：{}，类型：{}", email, type);
                return false;
            }
            
            if (!verification.getCode().equals(code)) {
                log.warn("验证码错误，邮箱：{}，类型：{}", email, type);
                return false;
            }
            
            // 标记验证码为已使用
            emailVerificationMapper.markAsUsed(verification.getId());
            
            log.info("验证码验证成功，邮箱：{}，类型：{}", email, type);
            return true;
            
        } catch (Exception e) {
            log.error("验证验证码失败，邮箱：{}，错误：{}", email, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 生成验证码
     */
    private String generateCode() {
        SecureRandom random = new SecureRandom();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }
    
    /**
     * 构建邮件内容
     */
    private String buildEmailContent(String code, String subject) {
        return String.format(
            "<html>" +
            "<body style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>" +
            "<div style='background-color: #f8f9fa; padding: 30px; border-radius: 10px;'>" +
            "<h2 style='color: #333; text-align: center; margin-bottom: 30px;'>%s</h2>" +
            "<p style='color: #666; font-size: 16px; line-height: 1.6;'>您好！</p>" +
            "<p style='color: #666; font-size: 16px; line-height: 1.6;'>您正在进行身份验证，验证码为：</p>" +
            "<div style='text-align: center; margin: 30px 0;'>" +
            "<span style='display: inline-block; background-color: #007bff; color: white; font-size: 32px; " +
            "font-weight: bold; padding: 15px 30px; border-radius: 8px; letter-spacing: 5px;'>%s</span>" +
            "</div>" +
            "<p style='color: #666; font-size: 16px; line-height: 1.6;'>验证码有效期为 %d 分钟，请及时使用。</p>" +
            "<p style='color: #999; font-size: 14px; line-height: 1.6; margin-top: 30px;'>" +
            "如果这不是您的操作，请忽略此邮件。</p>" +
            "<hr style='border: none; border-top: 1px solid #eee; margin: 30px 0;'>" +
            "<p style='color: #999; font-size: 12px; text-align: center;'>此邮件由系统自动发送，请勿回复。</p>" +
            "</div>" +
            "</body>" +
            "</html>",
            subject, code, CODE_EXPIRE_MINUTES
        );
    }
    
    /**
     * 发送邮件
     */
    private boolean sendEmail(String to, String subject, String content) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            
            helper.setFrom(fromEmail);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);
            
            mailSender.send(message);
            return true;
            
        } catch (Exception e) {
            log.error("发送邮件失败，收件人：{}，错误：{}", to, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public void cleanExpiredCodes() {
        try {
            int deleted = emailVerificationMapper.deleteExpiredCodes(LocalDateTime.now());
            if (deleted > 0) {
                log.info("清理过期验证码 {} 条", deleted);
            }
        } catch (Exception e) {
            log.error("清理过期验证码失败：{}", e.getMessage(), e);
        }
    }
}
