package qidian.it.springboot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import qidian.it.springboot.entity.Stock;
import qidian.it.springboot.mapper.StockMapper;
import qidian.it.springboot.service.StockService;

import java.util.List;
import java.util.Map;

/**
 * 库存服务实现类
 */
@Service
public class StockServiceImpl extends ServiceImpl<StockMapper, Stock> implements StockService {
    
    @Autowired
    private StockMapper stockMapper;
    
    @Override
    public Stock getStockByProductId(Long productId) {
        return stockMapper.selectByProductId(productId);
    }
    
    @Override
    public List<Map<String, Object>> getLowStockProducts() {
        return stockMapper.selectLowStockProducts();
    }
    
    @Override
    public List<Map<String, Object>> getAllStockWithProduct() {
        return stockMapper.selectAllStockWithProduct();
    }
    
    @Override
    public boolean updateStock(Long productId, Integer quantity) {
        if (quantity < 0) {
            return false; // 库存数量不能为负数
        }
        
        int result = stockMapper.updateStockByProductId(productId, quantity);
        return result > 0;
    }
    
    @Override
    @Transactional
    public boolean reduceStock(Long productId, Integer amount) {
        if (amount <= 0) {
            return false; // 减少数量必须大于0
        }
        
        // 检查当前库存是否足够
        Stock currentStock = getStockByProductId(productId);
        if (currentStock == null || currentStock.getQuantity() < amount) {
            return false; // 库存不足
        }
        
        int result = stockMapper.reduceStock(productId, amount);
        return result > 0;
    }
    
    @Override
    public boolean increaseStock(Long productId, Integer amount) {
        if (amount <= 0) {
            return false; // 增加数量必须大于0
        }
        
        int result = stockMapper.increaseStock(productId, amount);
        return result > 0;
    }
    
    @Override
    @Transactional
    public boolean batchUpdateStock(List<Map<String, Object>> stockList) {
        try {
            for (Map<String, Object> stockItem : stockList) {
                Long productId = Long.valueOf(stockItem.get("productId").toString());
                Integer quantity = Integer.valueOf(stockItem.get("quantity").toString());

                if (!updateStock(productId, quantity)) {
                    return false; // 如果任何一个更新失败，返回false
                }
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean checkStockAvailable(Long productId, Integer requiredQuantity) {
        if (productId == null || requiredQuantity == null || requiredQuantity <= 0) {
            return false;
        }

        Stock stock = getStockByProductId(productId);
        return stock != null && stock.getQuantity() >= requiredQuantity;
    }

    @Override
    public boolean batchCheckStockAvailable(List<Map<String, Object>> items) {
        if (items == null || items.isEmpty()) {
            return false;
        }

        for (Map<String, Object> item : items) {
            Long productId = Long.valueOf(item.get("productId").toString());
            Integer quantity = Integer.valueOf(item.get("quantity").toString());

            if (!checkStockAvailable(productId, quantity)) {
                return false;
            }
        }
        return true;
    }

    @Override
    @Transactional
    public boolean reserveStock(Long productId, Integer amount) {
        // 预扣库存：检查库存并减少（但不实际扣减，可以通过状态字段控制）
        return reduceStock(productId, amount);
    }

    @Override
    @Transactional
    public boolean releaseStock(Long productId, Integer amount) {
        // 释放预扣库存：增加库存
        return increaseStock(productId, amount);
    }

    @Override
    @Transactional
    public boolean confirmReduceStock(Long productId, Integer amount) {
        // 确认扣减库存：实际扣减库存
        return reduceStock(productId, amount);
    }

    @Override
    @Transactional
    public boolean batchReduceStock(List<Map<String, Object>> items) {
        if (items == null || items.isEmpty()) {
            return false;
        }

        try {
            for (Map<String, Object> item : items) {
                Long productId = Long.valueOf(item.get("productId").toString());
                Integer quantity = Integer.valueOf(item.get("quantity").toString());

                if (!reduceStock(productId, quantity)) {
                    throw new RuntimeException("商品ID " + productId + " 库存不足");
                }
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("批量扣减库存失败：" + e.getMessage());
        }
    }
}
