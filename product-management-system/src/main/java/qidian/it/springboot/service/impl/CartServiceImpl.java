package qidian.it.springboot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import qidian.it.springboot.entity.Cart;
import qidian.it.springboot.mapper.CartMapper;
import qidian.it.springboot.service.CartService;
import qidian.it.springboot.service.StockService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 购物车服务实现类
 */
@Service
public class CartServiceImpl extends ServiceImpl<CartMapper, Cart> implements CartService {

    @Autowired
    private CartMapper cartMapper;

    @Autowired
    private StockService stockService;
    
    @Override
    public List<Map<String, Object>> getCartWithProductByUserId(Long userId) {
        return cartMapper.selectCartWithProductByUserId(userId);
    }
    
    @Override
    @Transactional
    public boolean addToCart(Long userId, Long productId, Integer quantity) {
        if (userId == null || productId == null || quantity == null || quantity <= 0) {
            return false;
        }

        // 检查商品是否已在购物车中
        Cart existingCart = cartMapper.selectByUserIdAndProductId(userId, productId);

        if (existingCart != null) {
            // 商品已存在，增加数量
            int newQuantity = existingCart.getQuantity() + quantity;

            // 检查库存是否足够
            if (!stockService.checkStockAvailable(productId, newQuantity)) {
                throw new RuntimeException("库存不足，当前库存无法满足购买数量");
            }

            existingCart.setQuantity(newQuantity);
            return updateById(existingCart);
        } else {
            // 检查库存是否足够
            if (!stockService.checkStockAvailable(productId, quantity)) {
                throw new RuntimeException("库存不足，当前库存无法满足购买数量");
            }

            // 商品不存在，新增到购物车
            Cart cart = new Cart();
            cart.setUserId(userId);
            cart.setProductId(productId);
            cart.setQuantity(quantity);
            cart.setCreateTime(LocalDateTime.now());
            return save(cart);
        }
    }
    
    @Override
    public boolean updateCartQuantity(Long cartId, Integer quantity) {
        if (cartId == null || quantity == null || quantity <= 0) {
            return false;
        }

        // 获取购物车项信息
        Cart cart = getById(cartId);
        if (cart == null) {
            return false;
        }

        // 检查库存是否足够
        if (!stockService.checkStockAvailable(cart.getProductId(), quantity)) {
            throw new RuntimeException("库存不足，当前库存无法满足购买数量");
        }

        int result = cartMapper.updateQuantityById(cartId, quantity);
        return result > 0;
    }
    
    @Override
    public boolean removeFromCart(Long userId, Long productId) {
        if (userId == null || productId == null) {
            return false;
        }
        
        int result = cartMapper.deleteByUserIdAndProductId(userId, productId);
        return result > 0;
    }
    
    @Override
    @Transactional
    public boolean batchRemoveFromCart(Long userId, List<Long> productIds) {
        if (userId == null || productIds == null || productIds.isEmpty()) {
            return false;
        }
        
        QueryWrapper<Cart> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId)
               .in("product_id", productIds);
        
        return remove(wrapper);
    }
    
    @Override
    public boolean clearCart(Long userId) {
        if (userId == null) {
            return false;
        }
        
        int result = cartMapper.deleteByUserId(userId);
        return result >= 0; // 即使删除0条记录也算成功
    }
    
    @Override
    public Integer getCartItemCount(Long userId) {
        if (userId == null) {
            return 0;
        }
        
        Integer count = cartMapper.countByUserId(userId);
        return count != null ? count : 0;
    }
    
    @Override
    public Integer getCartTotalQuantity(Long userId) {
        if (userId == null) {
            return 0;
        }
        
        Integer total = cartMapper.sumQuantityByUserId(userId);
        return total != null ? total : 0;
    }
    
    @Override
    public boolean isProductInCart(Long userId, Long productId) {
        if (userId == null || productId == null) {
            return false;
        }
        
        Cart cart = cartMapper.selectByUserIdAndProductId(userId, productId);
        return cart != null;
    }
    
    @Override
    public Cart getCartItem(Long userId, Long productId) {
        if (userId == null || productId == null) {
            return null;
        }
        
        return cartMapper.selectByUserIdAndProductId(userId, productId);
    }
}
