package qidian.it.springboot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import qidian.it.springboot.entity.Product;
import qidian.it.springboot.entity.Stock;
import qidian.it.springboot.mapper.ProductMapper;
import qidian.it.springboot.service.ProductService;
import qidian.it.springboot.service.StockService;

import java.util.List;
import java.util.Map;

/**
 * 商品服务实现类
 */
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {
    
    @Autowired
    private ProductMapper productMapper;
    
    @Autowired
    private StockService stockService;
    
    @Override
    public List<Map<String, Object>> getProductsWithCategory() {
        return productMapper.selectProductsWithCategory();
    }
    
    @Override
    public List<Product> getProductsByCategory(Long categoryId) {
        return productMapper.selectByCategory(categoryId);
    }
    
    @Override
    public List<Product> getOnlineProducts() {
        return productMapper.selectOnlineProducts();
    }

    @Override
    public List<Map<String, Object>> getOnlineProductsWithCategory() {
        return productMapper.selectOnlineProductsWithCategory();
    }
    
    @Override
    public List<Product> searchProductsByName(String name) {
        return productMapper.selectByNameLike(name);
    }
    
    @Override
    public List<Map<String, Object>> getProductsWithStock() {
        return productMapper.selectProductsWithStock();
    }

    @Override
    public Map<String, Object> getProductWithStockById(Long productId) {
        return productMapper.selectProductWithStockById(productId);
    }
    
    @Override
    @Transactional
    public boolean addProduct(Product product) {
        // 保存商品信息
        boolean productSaved = save(product);
        
        if (productSaved) {
            // 创建对应的库存记录，初始库存为0
            Stock stock = new Stock(product.getId(), 0);
            stockService.save(stock);
        }
        
        return productSaved;
    }
    
    @Override
    public boolean updateProduct(Product product) {
        return updateById(product);
    }
    
    @Override
    @Transactional
    public boolean deleteProduct(Long productId) {
        // 先删除库存信息
        QueryWrapper<Stock> stockWrapper = new QueryWrapper<>();
        stockWrapper.eq("product_id", productId);
        stockService.remove(stockWrapper);
        
        // 再删除商品信息
        return removeById(productId);
    }
    
    @Override
    public boolean onlineProduct(Long productId) {
        Product product = getById(productId);
        if (product != null) {
            product.setStatus(1); // 1表示上架
            return updateById(product);
        }
        return false;
    }
    
    @Override
    public boolean offlineProduct(Long productId) {
        Product product = getById(productId);
        if (product != null) {
            product.setStatus(0); // 0表示下架
            return updateById(product);
        }
        return false;
    }
}
