package qidian.it.springboot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import qidian.it.springboot.entity.Cart;

import java.util.List;
import java.util.Map;

/**
 * 购物车服务接口
 */
public interface CartService extends IService<Cart> {
    
    /**
     * 获取用户购物车（包含商品信息）
     */
    List<Map<String, Object>> getCartWithProductByUserId(Long userId);
    
    /**
     * 添加商品到购物车
     * 如果商品已存在，则增加数量
     */
    boolean addToCart(Long userId, Long productId, Integer quantity);
    
    /**
     * 更新购物车商品数量
     */
    boolean updateCartQuantity(Long cartId, Integer quantity);
    
    /**
     * 从购物车删除商品
     */
    boolean removeFromCart(Long userId, Long productId);
    
    /**
     * 批量删除购物车商品
     */
    boolean batchRemoveFromCart(Long userId, List<Long> productIds);
    
    /**
     * 清空用户购物车
     */
    boolean clearCart(Long userId);
    
    /**
     * 获取用户购物车商品数量
     */
    Integer getCartItemCount(Long userId);
    
    /**
     * 获取用户购物车商品总数量
     */
    Integer getCartTotalQuantity(Long userId);
    
    /**
     * 检查商品是否在购物车中
     */
    boolean isProductInCart(Long userId, Long productId);
    
    /**
     * 获取购物车中的商品
     */
    Cart getCartItem(Long userId, Long productId);
}
