package qidian.it.springboot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Delete;
import qidian.it.springboot.entity.Cart;

import java.util.List;
import java.util.Map;

/**
 * 购物车Mapper接口
 */
@Mapper
public interface CartMapper extends BaseMapper<Cart> {
    
    /**
     * 查询用户购物车（包含商品信息）
     */
    @Select("SELECT c.id, c.user_id, c.product_id, c.quantity, c.create_time, " +
            "p.name, p.description, p.price, p.image_url, " +
            "cat.name as category_name " +
            "FROM cart c " +
            "INNER JOIN product p ON c.product_id = p.id " +
            "LEFT JOIN category cat ON p.category_id = cat.id " +
            "WHERE c.user_id = #{userId} " +
            "ORDER BY c.create_time DESC")
    List<Map<String, Object>> selectCartWithProductByUserId(Long userId);
    
    /**
     * 查询用户购物车中的某个商品
     */
    @Select("SELECT * FROM cart WHERE user_id = #{userId} AND product_id = #{productId}")
    Cart selectByUserIdAndProductId(Long userId, Long productId);
    
    /**
     * 更新购物车商品数量
     */
    @Update("UPDATE cart SET quantity = #{quantity} WHERE id = #{cartId}")
    int updateQuantityById(Long cartId, Integer quantity);
    
    /**
     * 删除用户购物车中的商品
     */
    @Delete("DELETE FROM cart WHERE user_id = #{userId} AND product_id = #{productId}")
    int deleteByUserIdAndProductId(Long userId, Long productId);
    
    /**
     * 清空用户购物车
     */
    @Delete("DELETE FROM cart WHERE user_id = #{userId}")
    int deleteByUserId(Long userId);
    
    /**
     * 统计用户购物车商品数量
     */
    @Select("SELECT COUNT(*) FROM cart WHERE user_id = #{userId}")
    Integer countByUserId(Long userId);
    
    /**
     * 统计用户购物车商品总数量
     */
    @Select("SELECT COALESCE(SUM(quantity), 0) FROM cart WHERE user_id = #{userId}")
    Integer sumQuantityByUserId(Long userId);
}
