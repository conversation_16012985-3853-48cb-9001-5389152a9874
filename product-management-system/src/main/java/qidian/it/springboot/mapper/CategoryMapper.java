package qidian.it.springboot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import qidian.it.springboot.entity.Category;

import java.util.List;

/**
 * 商品分类Mapper接口
 */
@Mapper
public interface CategoryMapper extends BaseMapper<Category> {
    
    /**
     * 查询所有分类（按ID排序）
     */
    @Select("SELECT * FROM category ORDER BY id")
    List<Category> selectAllCategories();
    
    /**
     * 根据分类名称查询分类
     */
    @Select("SELECT * FROM category WHERE name = #{name}")
    Category selectByName(String name);
    
    /**
     * 查询分类下的商品数量
     */
    @Select("SELECT COUNT(*) FROM product WHERE category_id = #{categoryId}")
    Integer countProductsByCategory(Long categoryId);
}
