package qidian.it.springboot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import qidian.it.springboot.entity.Stock;

import java.util.List;
import java.util.Map;

/**
 * 库存Mapper接口
 */
@Mapper
public interface StockMapper extends BaseMapper<Stock> {
    
    /**
     * 根据商品ID查询库存
     */
    @Select("SELECT * FROM stock WHERE product_id = #{productId}")
    Stock selectByProductId(Long productId);
    
    /**
     * 查询库存不足的商品
     */
    @Select("SELECT p.name, p.price, s.quantity, s.min_stock " +
            "FROM product p " +
            "INNER JOIN stock s ON p.id = s.product_id " +
            "WHERE s.quantity <= s.min_stock " +
            "ORDER BY s.quantity")
    List<Map<String, Object>> selectLowStockProducts();
    
    /**
     * 更新库存数量
     */
    @Update("UPDATE stock SET quantity = #{quantity} WHERE product_id = #{productId}")
    int updateStockByProductId(Long productId, Integer quantity);
    
    /**
     * 减少库存（用于下单）
     */
    @Update("UPDATE stock SET quantity = quantity - #{amount} WHERE product_id = #{productId} AND quantity >= #{amount}")
    int reduceStock(Long productId, Integer amount);
    
    /**
     * 增加库存（用于退货）
     */
    @Update("UPDATE stock SET quantity = quantity + #{amount} WHERE product_id = #{productId}")
    int increaseStock(Long productId, Integer amount);
    
    /**
     * 查询所有库存信息
     */
    @Select("SELECT s.*, p.name as product_name " +
            "FROM stock s " +
            "LEFT JOIN product p ON s.product_id = p.id " +
            "ORDER BY s.id")
    List<Map<String, Object>> selectAllStockWithProduct();
}
