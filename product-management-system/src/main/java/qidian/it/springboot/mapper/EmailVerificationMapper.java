package qidian.it.springboot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import qidian.it.springboot.entity.EmailVerification;

import java.time.LocalDateTime;

/**
 * 邮箱验证码Mapper接口
 */
@Mapper
public interface EmailVerificationMapper extends BaseMapper<EmailVerification> {
    
    /**
     * 查询有效的验证码
     */
    @Select("SELECT * FROM email_verification WHERE email = #{email} AND type = #{type} " +
            "AND used = 0 AND expire_time > #{now} ORDER BY create_time DESC LIMIT 1")
    EmailVerification selectValidCode(@Param("email") String email, 
                                    @Param("type") Integer type, 
                                    @Param("now") LocalDateTime now);
    
    /**
     * 标记验证码为已使用
     */
    @Update("UPDATE email_verification SET used = 1 WHERE id = #{id}")
    int markAsUsed(@Param("id") Long id);
    
    /**
     * 删除过期的验证码
     */
    @Update("DELETE FROM email_verification WHERE expire_time < #{now}")
    int deleteExpiredCodes(@Param("now") LocalDateTime now);
    
    /**
     * 检查邮箱在指定时间内是否已发送验证码
     */
    @Select("SELECT COUNT(*) FROM email_verification WHERE email = #{email} AND type = #{type} " +
            "AND create_time > #{since}")
    int countRecentCodes(@Param("email") String email, 
                        @Param("type") Integer type, 
                        @Param("since") LocalDateTime since);
}
