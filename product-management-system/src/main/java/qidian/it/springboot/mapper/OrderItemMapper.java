package qidian.it.springboot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import qidian.it.springboot.entity.OrderItem;

import java.util.List;
import java.util.Map;

/**
 * 订单项Mapper接口
 */
@Mapper
public interface OrderItemMapper extends BaseMapper<OrderItem> {
    
    /**
     * 根据订单ID查询订单项
     */
    @Select("SELECT * FROM order_item WHERE order_id = #{orderId}")
    List<OrderItem> selectByOrderId(Long orderId);
    
    /**
     * 查询订单项详情（包含商品信息）
     */
    @Select("SELECT oi.*, p.name as product_name, p.description, p.image_url, c.name as category_name " +
            "FROM order_item oi " +
            "INNER JOIN product p ON oi.product_id = p.id " +
            "LEFT JOIN category c ON p.category_id = c.id " +
            "WHERE oi.order_id = #{orderId}")
    List<Map<String, Object>> selectOrderItemsWithProduct(Long orderId);
    
    /**
     * 统计订单项数量
     */
    @Select("SELECT COUNT(*) FROM order_item WHERE order_id = #{orderId}")
    Integer countByOrderId(Long orderId);
}
