package qidian.it.springboot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import qidian.it.springboot.entity.Product;

import java.util.List;
import java.util.Map;

/**
 * 商品Mapper接口
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product> {
    
    /**
     * 查询所有商品及其分类信息
     */
    @Select("SELECT p.*, c.name as category_name " +
            "FROM product p " +
            "LEFT JOIN category c ON p.category_id = c.id " +
            "ORDER BY p.id")
    List<Map<String, Object>> selectProductsWithCategory();
    
    /**
     * 根据分类ID查询商品
     */
    @Select("SELECT * FROM product WHERE category_id = #{categoryId} AND status = 1 ORDER BY id")
    List<Product> selectByCategory(Long categoryId);
    
    /**
     * 查询上架商品
     */
    @Select("SELECT * FROM product WHERE status = 1 ORDER BY id")
    List<Product> selectOnlineProducts();

    /**
     * 查询上架商品及其分类信息
     */
    @Select("SELECT p.*, c.name as category_name " +
            "FROM product p " +
            "LEFT JOIN category c ON p.category_id = c.id " +
            "WHERE p.status = 1 " +
            "ORDER BY p.id")
    List<Map<String, Object>> selectOnlineProductsWithCategory();
    
    /**
     * 根据商品名称模糊查询
     */
    @Select("SELECT * FROM product WHERE name LIKE CONCAT('%', #{name}, '%') ORDER BY id")
    List<Product> selectByNameLike(String name);
    
    /**
     * 查询商品及其库存信息
     */
    @Select("SELECT p.*, s.quantity, s.min_stock, " +
            "CASE WHEN s.quantity = 0 THEN '缺货' " +
            "     WHEN s.quantity <= s.min_stock THEN '库存不足' " +
            "     ELSE '库存充足' END as stock_status " +
            "FROM product p " +
            "LEFT JOIN stock s ON p.id = s.product_id " +
            "ORDER BY p.id")
    List<Map<String, Object>> selectProductsWithStock();

    /**
     * 根据商品ID查询商品及其库存信息
     */
    @Select("SELECT p.*, s.quantity, s.min_stock, " +
            "CASE WHEN s.quantity = 0 THEN '缺货' " +
            "     WHEN s.quantity <= s.min_stock THEN '库存不足' " +
            "     ELSE '库存充足' END as stock_status " +
            "FROM product p " +
            "LEFT JOIN stock s ON p.id = s.product_id " +
            "WHERE p.id = #{productId}")
    Map<String, Object> selectProductWithStockById(Long productId);
}
