<template>
  <div class="category-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>分类管理</h2>
        <p class="header-desc">管理商品分类，支持添加、编辑和删除操作</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDialog" :icon="Plus">
          添加分类
        </el-button>
        <el-button @click="loadCategories" :icon="Refresh" :loading="loading">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 分类列表 -->
    <div class="category-list">
      <el-table 
        :data="categories" 
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        empty-text="暂无分类数据"
      >
        <el-table-column prop="id" label="分类ID" width="100" align="center" />
        
        <el-table-column prop="name" label="分类名称" min-width="200">
          <template #default="{ row }">
            <div class="category-name">
              <el-icon class="category-icon"><Grid /></el-icon>
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="商品数量" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.productCount > 0 ? 'success' : 'info'">
              {{ row.productCount || 0 }} 个商品
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" align="center">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="showEditDialog(row)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="handleDelete(row)"
              :icon="Delete"
              :disabled="row.productCount > 0"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加/编辑分类对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <el-form
        ref="categoryFormRef"
        :model="categoryForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input
            v-model="categoryForm.name"
            placeholder="请输入分类名称"
            maxlength="50"
            show-word-limit
            clearable
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleSubmit"
            :loading="submitting"
          >
            {{ isEdit ? '更新' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Edit, Delete, Grid } from '@element-plus/icons-vue'
import { categoryAPI } from '@/api/category'

export default {
  name: 'CategoryManagement',
  components: {
    Plus,
    Refresh,
    Edit,
    Delete,
    Grid
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const submitting = ref(false)
    const categories = ref([])
    const dialogVisible = ref(false)
    const isEdit = ref(false)
    const categoryFormRef = ref(null)
    
    // 表单数据
    const categoryForm = reactive({
      id: null,
      name: ''
    })
    
    // 表单验证规则
    const formRules = {
      name: [
        { required: true, message: '请输入分类名称', trigger: 'blur' },
        { min: 1, max: 50, message: '分类名称长度在 1 到 50 个字符', trigger: 'blur' },
        { 
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\s]+$/, 
          message: '分类名称只能包含中文、英文、数字和空格', 
          trigger: 'blur' 
        }
      ]
    }
    
    // 计算属性
    const dialogTitle = computed(() => {
      return isEdit.value ? '编辑分类' : '添加分类'
    })
    
    // 加载分类列表
    const loadCategories = async () => {
      loading.value = true
      try {
        const response = await categoryAPI.getAllCategories()
        if (response.data.success) {
          // 为每个分类获取商品数量
          const categoriesData = response.data.data
          for (let category of categoriesData) {
            try {
              const detailResponse = await categoryAPI.getCategoryDetail(category.id)
              if (detailResponse.data.success) {
                category.productCount = detailResponse.data.data.productCount
              } else {
                category.productCount = 0
              }
            } catch (error) {
              console.error(`获取分类 ${category.id} 详情失败:`, error)
              category.productCount = 0
            }
          }
          categories.value = categoriesData
          ElMessage.success('分类列表加载成功')
        } else {
          ElMessage.error(response.data.message || '获取分类列表失败')
        }
      } catch (error) {
        console.error('获取分类列表失败:', error)
        ElMessage.error('获取分类列表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 显示添加对话框
    const showAddDialog = () => {
      isEdit.value = false
      dialogVisible.value = true
      resetForm()
    }
    
    // 显示编辑对话框
    const showEditDialog = (category) => {
      isEdit.value = true
      dialogVisible.value = true
      categoryForm.id = category.id
      categoryForm.name = category.name
    }
    
    // 重置表单
    const resetForm = () => {
      categoryForm.id = null
      categoryForm.name = ''
      if (categoryFormRef.value) {
        categoryFormRef.value.resetFields()
      }
    }
    
    // 提交表单
    const handleSubmit = async () => {
      if (!categoryFormRef.value) return
      
      try {
        await categoryFormRef.value.validate()
        submitting.value = true
        
        if (isEdit.value) {
          // 更新分类
          const response = await categoryAPI.updateCategory({
            id: categoryForm.id,
            name: categoryForm.name.trim()
          })
          
          if (response.data.success) {
            ElMessage.success('分类更新成功')
            dialogVisible.value = false
            loadCategories()
          } else {
            ElMessage.error(response.data.message || '分类更新失败')
          }
        } else {
          // 添加分类
          const response = await categoryAPI.addCategory({
            name: categoryForm.name.trim()
          })
          
          if (response.data.success) {
            ElMessage.success('分类添加成功')
            dialogVisible.value = false
            loadCategories()
          } else {
            ElMessage.error(response.data.message || '分类添加失败')
          }
        }
      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          console.error('提交分类失败:', error)
          ElMessage.error('操作失败，请重试')
        }
      } finally {
        submitting.value = false
      }
    }
    
    // 删除分类
    const handleDelete = async (category) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除分类"${category.name}"吗？此操作不可恢复。`,
          '删除确认',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
          }
        )
        
        const response = await categoryAPI.deleteCategory(category.id)
        if (response.data.success) {
          ElMessage.success('分类删除成功')
          loadCategories()
        } else {
          ElMessage.error(response.data.message || '分类删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除分类失败:', error)
          ElMessage.error('删除失败，请重试')
        }
      }
    }
    
    // 组件挂载时加载数据
    onMounted(() => {
      loadCategories()
    })
    
    return {
      loading,
      submitting,
      categories,
      dialogVisible,
      isEdit,
      categoryFormRef,
      categoryForm,
      formRules,
      dialogTitle,
      loadCategories,
      showAddDialog,
      showEditDialog,
      resetForm,
      handleSubmit,
      handleDelete
    }
  }
}
</script>

<style scoped>
.category-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 600;
}

.header-desc {
  margin: 0;
  color: #909399;
  font-size: 14px;
  line-height: 1.5;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 分类列表样式 */
.category-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.category-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-icon {
  color: #409EFF;
  font-size: 16px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 0;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 16px 0;
}

:deep(.el-table .el-button) {
  margin: 0 4px;
}

:deep(.el-table .el-button + .el-button) {
  margin-left: 8px;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-dialog__header) {
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px 24px 24px;
  border-top: 1px solid #ebeef5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-input__wrapper:hover) {
  border-color: #409EFF;
}

/* 标签样式 */
:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
}

/* 按钮样式 */
:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #3a8ee6 0%, #337ecc 100%);
}

:deep(.el-button--danger) {
  background: linear-gradient(135deg, #f56c6c 0%, #f45656 100%);
  border: none;
}

:deep(.el-button--danger:hover) {
  background: linear-gradient(135deg, #f45656 0%, #e54545 100%);
}

:deep(.el-button:disabled) {
  background: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

/* 加载状态样式 */
:deep(.el-loading-mask) {
  border-radius: 12px;
}

/* 空状态样式 */
:deep(.el-table__empty-text) {
  color: #909399;
  font-size: 14px;
  padding: 60px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-management {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px;
  }

  .header-right {
    justify-content: flex-start;
  }

  :deep(.el-table .el-button) {
    padding: 8px 12px;
    font-size: 12px;
  }

  :deep(.el-dialog) {
    width: 90%;
    margin: 5vh auto;
  }
}

@media (max-width: 480px) {
  .header-left h2 {
    font-size: 20px;
  }

  .header-desc {
    font-size: 13px;
  }

  .header-right {
    flex-direction: column;
  }

  :deep(.el-table .el-table__cell) {
    padding: 12px 8px;
  }

  .category-name {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
