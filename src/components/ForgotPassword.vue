<template>
  <div class="forgot-password-container">
    <div class="forgot-password-card">
      <div class="card-header">
        <h2>忘记密码</h2>
        <p>请输入您的用户名，我们将发送验证码到您绑定的邮箱</p>
      </div>
      
      <el-form 
        ref="forgotPasswordForm" 
        :model="form" 
        :rules="rules" 
        label-width="0px"
        class="forgot-password-form"
      >
        <!-- 步骤1：输入用户名 -->
        <div v-if="step === 1">
          <el-form-item prop="username">
            <el-input
              v-model="form.username"
              placeholder="请输入用户名"
              prefix-icon="el-icon-user"
              size="large"
              :disabled="loading"
            />
          </el-form-item>

          <!-- 显示脱敏邮箱 -->
          <div v-if="maskedEmail" class="email-hint">
            <el-alert
              :title="`验证码将发送到：${maskedEmail}`"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
          
          <el-form-item>
            <el-button 
              type="primary" 
              size="large" 
              :loading="loading"
              :disabled="countdown > 0"
              @click="sendCode"
              class="full-width-btn"
            >
              <span v-if="countdown > 0">{{ countdown }}s 后重新发送</span>
              <span v-else>发送验证码</span>
            </el-button>
          </el-form-item>
        </div>
        
        <!-- 步骤2：输入验证码和新密码 -->
        <div v-if="step === 2">
          <el-form-item prop="code">
            <el-input
              v-model="form.code"
              placeholder="请输入6位验证码"
              prefix-icon="el-icon-key"
              size="large"
              maxlength="6"
              :disabled="loading"
            />
          </el-form-item>
          
          <el-form-item prop="newPassword">
            <el-input
              v-model="form.newPassword"
              type="password"
              placeholder="请输入新密码（至少6位）"
              prefix-icon="el-icon-lock"
              size="large"
              show-password
              :disabled="loading"
            />
          </el-form-item>
          
          <el-form-item prop="confirmPassword">
            <el-input
              v-model="form.confirmPassword"
              type="password"
              placeholder="请确认新密码"
              prefix-icon="el-icon-lock"
              size="large"
              show-password
              :disabled="loading"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button 
              type="primary" 
              size="large" 
              :loading="loading"
              @click="resetPassword"
              class="full-width-btn"
            >
              重置密码
            </el-button>
          </el-form-item>
          
          <el-form-item>
            <el-button 
              size="large" 
              @click="goBack"
              class="full-width-btn"
            >
              返回上一步
            </el-button>
          </el-form-item>
        </div>
      </el-form>
      
      <div class="card-footer">
        <el-link type="primary" @click="goToLogin">返回登录</el-link>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, ref, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import api from '@/api/user'

export default {
  name: 'ForgotPassword',
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const step = ref(1)
    const loading = ref(false)
    const countdown = ref(0)
    let countdownTimer = null
    
    const form = reactive({
      username: '',
      code: '',
      newPassword: '',
      confirmPassword: ''
    })

    const maskedEmail = ref('')
    
    // 表单验证规则
    const rules = reactive({
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, message: '用户名长度不能少于3位', trigger: 'blur' }
      ],
      code: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { len: 6, message: '验证码必须是6位数字', trigger: 'blur' }
      ],
      newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认新密码', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (value !== form.newPassword) {
              callback(new Error('两次输入的密码不一致'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ]
    })
    
    // 发送验证码
    const sendCode = async () => {
      try {
        loading.value = true

        const response = await api.post('/forgot-password/send-code', {
          username: form.username
        })

        if (response.success) {
          ElMessage.success(response.message)
          maskedEmail.value = response.email || ''
          step.value = 2
          startCountdown()
        } else {
          ElMessage.error(response.message)
        }
      } catch (error) {
        console.error('发送验证码失败:', error)
        ElMessage.error('发送验证码失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
    
    // 重置密码
    const resetPassword = async () => {
      try {
        loading.value = true

        const response = await api.post('/forgot-password/reset-password', {
          username: form.username,
          code: form.code,
          newPassword: form.newPassword
        })

        if (response.success) {
          ElMessage.success(response.message)
          setTimeout(() => {
            router.push('/user/login')
          }, 2000)
        } else {
          ElMessage.error(response.message)
        }
      } catch (error) {
        console.error('重置密码失败:', error)
        ElMessage.error('重置密码失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
    
    // 开始倒计时
    const startCountdown = () => {
      countdown.value = 60
      countdownTimer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(countdownTimer)
        }
      }, 1000)
    }
    
    // 返回上一步
    const goBack = () => {
      step.value = 1
      maskedEmail.value = ''
      form.code = ''
      form.newPassword = ''
      form.confirmPassword = ''
    }
    
    // 返回登录
    const goToLogin = () => {
      router.push('/user/login')
    }
    
    // 清理定时器
    onUnmounted(() => {
      if (countdownTimer) {
        clearInterval(countdownTimer)
      }
    })
    
    return {
      step,
      loading,
      countdown,
      form,
      rules,
      maskedEmail,
      sendCode,
      resetPassword,
      goBack,
      goToLogin
    }
  }
}
</script>

<style scoped>
.forgot-password-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.forgot-password-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
}

.card-header {
  text-align: center;
  margin-bottom: 30px;
}

.card-header h2 {
  color: #333;
  margin-bottom: 10px;
  font-weight: 600;
}

.card-header p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.forgot-password-form {
  margin-bottom: 20px;
}

.full-width-btn {
  width: 100%;
  height: 45px;
  font-size: 16px;
  border-radius: 6px;
}

.card-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

:deep(.el-input__inner) {
  height: 45px;
  border-radius: 6px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

.email-hint {
  margin-bottom: 20px;
}

.email-hint :deep(.el-alert) {
  border-radius: 6px;
}
</style>
