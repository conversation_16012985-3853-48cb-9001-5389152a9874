<template>
  <div class="admin-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>添加新管理员</h2>
        <p class="header-desc">管理系统管理员账户，支持添加新管理员</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDialog" :icon="Plus">
          添加管理员
        </el-button>
        <el-button @click="loadAdmins" :icon="Refresh" :loading="loading">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 管理员列表 -->
    <div class="admin-list-card">
      <el-table 
        :data="admins" 
        v-loading="loading"
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" min-width="150" />
        <el-table-column prop="email" label="邮箱" min-width="200" />
        <el-table-column prop="created_at" label="创建时间" min-width="180">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="showEditDialog(scope.row)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
              :icon="Delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加/编辑管理员对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <el-form
        ref="adminFormRef"
        :model="adminForm"
        :rules="formRules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="adminForm.username"
            placeholder="请输入管理员用户名"
            :disabled="isEdit"
          />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="adminForm.email"
            placeholder="请输入管理员邮箱"
            type="email"
          />
        </el-form-item>
        
        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input
            v-model="adminForm.password"
            placeholder="请输入密码"
            type="password"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword" v-if="!isEdit">
          <el-input
            v-model="adminForm.confirmPassword"
            placeholder="请再次输入密码"
            type="password"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="adminForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleSubmit"
            :loading="submitting"
          >
            {{ isEdit ? '更新' : '添加' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Edit, Delete } from '@element-plus/icons-vue'
import adminAPI from '@/api/admin.js'

export default {
  name: 'AdminManagement',
  components: {
    Plus,
    Refresh,
    Edit,
    Delete
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const submitting = ref(false)
    const admins = ref([])
    const dialogVisible = ref(false)
    const isEdit = ref(false)
    const adminFormRef = ref(null)
    
    // 表单数据
    const adminForm = reactive({
      id: null,
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      status: 1
    })
    
    // 表单验证规则
    const formRules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
        { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认密码', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (value !== adminForm.password) {
              callback(new Error('两次输入的密码不一致'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ]
    }
    
    // 计算属性
    const dialogTitle = computed(() => {
      return isEdit.value ? '编辑管理员' : '添加管理员'
    })
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
    
    // 加载管理员列表
    const loadAdmins = async () => {
      loading.value = true
      try {
        const response = await adminAPI.getAllAdmins()
        if (response.data.success) {
          admins.value = response.data.data || []
          ElMessage.success('管理员列表加载成功')
        } else {
          ElMessage.error(response.data.message || '获取管理员列表失败')
        }
      } catch (error) {
        console.error('获取管理员列表失败:', error)
        ElMessage.error('获取管理员列表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 显示添加对话框
    const showAddDialog = () => {
      isEdit.value = false
      dialogVisible.value = true
      resetForm()
    }
    
    // 显示编辑对话框
    const showEditDialog = (admin) => {
      isEdit.value = true
      dialogVisible.value = true
      adminForm.id = admin.id
      adminForm.username = admin.username
      adminForm.email = admin.email
      adminForm.status = admin.status
      adminForm.password = ''
      adminForm.confirmPassword = ''
    }
    
    // 重置表单
    const resetForm = () => {
      if (adminFormRef.value) {
        adminFormRef.value.resetFields()
      }
      adminForm.id = null
      adminForm.username = ''
      adminForm.email = ''
      adminForm.password = ''
      adminForm.confirmPassword = ''
      adminForm.status = 1
    }
    
    // 提交表单
    const handleSubmit = async () => {
      if (!adminFormRef.value) return
      
      try {
        await adminFormRef.value.validate()
        submitting.value = true
        
        if (isEdit.value) {
          // 更新管理员
          const response = await adminAPI.updateAdmin({
            id: adminForm.id,
            email: adminForm.email.trim(),
            status: adminForm.status
          })
          
          if (response.data.success) {
            ElMessage.success('管理员更新成功')
            dialogVisible.value = false
            loadAdmins()
          } else {
            ElMessage.error(response.data.message || '管理员更新失败')
          }
        } else {
          // 添加管理员
          const response = await adminAPI.addAdmin({
            username: adminForm.username.trim(),
            email: adminForm.email.trim(),
            password: adminForm.password,
            status: adminForm.status
          })
          
          if (response.data.success) {
            ElMessage.success('管理员添加成功')
            dialogVisible.value = false
            loadAdmins()
          } else {
            ElMessage.error(response.data.message || '管理员添加失败')
          }
        }
      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          console.error('提交管理员失败:', error)
          ElMessage.error('操作失败，请重试')
        }
      } finally {
        submitting.value = false
      }
    }
    
    // 删除管理员
    const handleDelete = async (admin) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除管理员 "${admin.username}" 吗？此操作不可恢复。`,
          '删除确认',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
          }
        )
        
        const response = await adminAPI.deleteAdmin(admin.id)
        if (response.data.success) {
          ElMessage.success('管理员删除成功')
          loadAdmins()
        } else {
          ElMessage.error(response.data.message || '管理员删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除管理员失败:', error)
          ElMessage.error('删除失败，请重试')
        }
      }
    }
    
    // 组件挂载时执行
    onMounted(() => {
      loadAdmins()
    })
    
    return {
      loading,
      submitting,
      admins,
      dialogVisible,
      isEdit,
      adminFormRef,
      adminForm,
      formRules,
      dialogTitle,
      formatDate,
      loadAdmins,
      showAddDialog,
      showEditDialog,
      resetForm,
      handleSubmit,
      handleDelete
    }
  }
}
</script>

<style scoped>
.admin-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 600;
}

.header-desc {
  margin: 0;
  color: #909399;
  font-size: 14px;
  line-height: 1.5;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 管理员列表卡片样式 */
.admin-list-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 表格样式优化 */
.el-table {
  border-radius: 12px;
}

.el-table .el-table__header-wrapper {
  border-radius: 12px 12px 0 0;
}

.el-table .el-table__body-wrapper {
  border-radius: 0 0 12px 12px;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表单样式优化 */
.el-form-item {
  margin-bottom: 20px;
}

.el-input {
  border-radius: 8px;
}

.el-button {
  border-radius: 8px;
  font-weight: 500;
}

/* 状态标签样式 */
.el-tag {
  border-radius: 6px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-management {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    justify-content: flex-start;
  }

  .admin-list-card {
    overflow-x: auto;
  }
}

@media (max-width: 480px) {
  .header-right {
    flex-direction: column;
    gap: 8px;
  }

  .el-button {
    width: 100%;
  }
}
</style>
