<template>
  <div class="admin-login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="floating-shape shape-4"></div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 返回按钮 -->
      <div class="back-button">
        <button
          @click="goBack"
          class="back-btn"
        >
          <el-icon class="back-icon">
            <ArrowLeft />
          </el-icon>
          <span class="back-text">返回主页</span>
        </button>
      </div>

      <!-- 登录卡片 -->
      <div class="login-card">
        <div class="card-content">
          <div class="login-header">
            <div class="icon-wrapper">
              <div class="icon-bg">
                <el-icon class="login-icon">
                  <UserFilled />
                </el-icon>
              </div>
            </div>
            <h1 class="login-title">管理员登录</h1>
            <p class="login-subtitle">系统管理员专用登录入口</p>
          </div>
          <!-- 登录表单 -->
          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            class="login-form"
            size="large"
          >
            <el-form-item prop="username">
              <div class="input-wrapper">
                <el-input
                  v-model="loginForm.username"
                  placeholder="请输入管理员账号"
                  :prefix-icon="User"
                  clearable
                  class="custom-input"
                />
              </div>
            </el-form-item>

            <el-form-item prop="password">
              <div class="input-wrapper">
                <el-input
                  v-model="loginForm.password"
                  type="password"
                  placeholder="请输入管理员密码"
                  :prefix-icon="Lock"
                  show-password
                  clearable
                  class="custom-input"
                />
              </div>
            </el-form-item>

            <el-form-item prop="captcha" v-if="showCaptcha">
              <div class="captcha-container">
                <div class="input-wrapper captcha-input">
                  <el-input
                    v-model="loginForm.captcha"
                    placeholder="请输入验证码"
                    :prefix-icon="Key"
                    clearable
                    class="custom-input"
                  />
                </div>
                <div class="captcha-code" @click="refreshCaptcha">
                  {{ captchaCode }}
                </div>
              </div>
            </el-form-item>

            <el-form-item>
              <div class="form-options">
                <el-checkbox v-model="loginForm.remember" class="remember-checkbox">记住登录状态</el-checkbox>
                <el-link type="primary" :underline="false" class="forgot-link">忘记密码？</el-link>
              </div>
            </el-form-item>

            <el-form-item>
              <button
                type="button"
                class="login-button"
                :disabled="loading"
                @click="handleLogin"
              >
                <span class="button-content">
                  <el-icon v-if="!loading" class="button-icon"><Lock /></el-icon>
                  <span class="button-text">{{ loading ? '登录中...' : '管理员登录' }}</span>
                </span>
                <div class="button-glow"></div>
              </button>
            </el-form-item>
          </el-form>
          <!-- 安全提示 -->
          <div class="security-notice">
            <div class="security-alert">
              <el-icon class="security-icon"><Warning /></el-icon>
              <div class="security-content">
                <div class="security-title">安全提示</div>
                <div class="security-description">管理员账号具有系统最高权限，请妥善保管账号密码，不要在公共场所登录。</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, User, Lock, UserFilled, Key, Warning } from '@element-plus/icons-vue'
import { adminAPI } from '@/api/user'

export default {
  name: 'AdminLogin',
  components: {
    UserFilled
  },
  setup() {
    const router = useRouter()
    const loginFormRef = ref()
    const loading = ref(false)
    const showCaptcha = ref(true)
    const captchaCode = ref('')
    
    // 登录表单数据
    const loginForm = reactive({
      username: '',
      password: '',
      captcha: '',
      remember: false
    })
    
    // 表单验证规则
    const loginRules = {
      username: [
        { required: true, message: '请输入管理员账号', trigger: 'blur' },
        { min: 3, max: 20, message: '账号长度在 3 到 20 个字符', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入管理员密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
      ],
      captcha: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { len: 4, message: '验证码为4位字符', trigger: 'blur' }
      ]
    }
    
    // 生成验证码
    const generateCaptcha = () => {
      const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
      let result = ''
      for (let i = 0; i < 4; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      return result
    }
    
    // 刷新验证码
    const refreshCaptcha = () => {
      captchaCode.value = generateCaptcha()
    }
    
    // 返回登录选择页
    const goBack = () => {
      router.push('/')
    }
    
    // 处理登录
    const handleLogin = async () => {
      if (!loginFormRef.value) return
      
      try {
        const valid = await loginFormRef.value.validate()
        if (valid) {
          // 验证验证码
          if (loginForm.captcha.toLowerCase() !== captchaCode.value.toLowerCase()) {
            ElMessage.error('验证码错误')
            refreshCaptcha()
            loginForm.captcha = ''
            return
          }
          
          loading.value = true

          try {
            // 调用管理员登录API
            const response = await adminAPI.login({
              username: loginForm.username,
              password: loginForm.password
            })

            if (response.success) {
              ElMessage.success(response.message || '管理员登录成功！')

              // 保存管理员信息到localStorage
              localStorage.setItem('adminInfo', JSON.stringify(response.data))
              localStorage.setItem('isAdminLoggedIn', 'true')

              // 登录成功后跳转到管理员主页
              router.push('/admin/dashboard')

              console.log('管理员登录成功，管理员信息:', response.data)
            } else {
              ElMessage.error(response.message || '管理员登录失败')
              refreshCaptcha()
              loginForm.captcha = ''
            }
            
          } catch (error) {
            console.error('管理员登录请求失败:', error)
            ElMessage.error(error.message || '登录失败，请检查网络连接')
            refreshCaptcha()
            loginForm.captcha = ''
          } finally {
            loading.value = false
          }
        }
      } catch (error) {
        console.log('表单验证失败:', error)
      }
    }
    
    // 组件挂载时生成验证码
    onMounted(() => {
      refreshCaptcha()
    })
    
    return {
      loginFormRef,
      loginForm,
      loginRules,
      loading,
      showCaptcha,
      captchaCode,
      goBack,
      handleLogin,
      refreshCaptcha,
      ArrowLeft,
      User,
      Lock,
      UserFilled,
      Key,
      Warning
    }
  }
}
</script>

<style scoped>
/* 主容器 */
.admin-login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6a00 0%, #ff8533 50%, #ffb366 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 20px;
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 8s ease-in-out infinite;
}

.shape-1 {
  width: 120px;
  height: 120px;
  top: 15%;
  left: 10%;
  animation-delay: 0s;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.15), rgba(255, 200, 100, 0.2));
}

.shape-2 {
  width: 80px;
  height: 80px;
  top: 70%;
  right: 15%;
  animation-delay: 2s;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 179, 102, 0.15));
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.12), rgba(255, 133, 51, 0.18));
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 30%;
  right: 25%;
  animation-delay: 6s;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.08), rgba(255, 200, 100, 0.12));
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-30px) rotate(180deg) scale(1.1);
    opacity: 0.3;
  }
}

/* 主要内容 */
.main-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 420px;
}

/* 返回按钮 */
.back-button {
  position: absolute;
  top: 5px;
  left: 0;
}

.back-btn {
  background: rgba(255, 255, 255, 0.25);
  border: 2px solid rgba(255, 255, 255, 0.4);
  color: white;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  outline: none;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.35);
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateX(-8px) scale(1.05);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.back-icon {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.back-btn:hover .back-icon {
  transform: translateX(-2px);
}

.back-text {
  white-space: nowrap;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 登录卡片 */
.login-card {
  border-radius: 24px;
  overflow: hidden;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.92));
  backdrop-filter: blur(20px);
  border: 3px solid #ff6a00;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  margin-top: 55px;
}

.card-content {
  padding: 45px;
  position: relative;
  z-index: 1;
}

/* 登录头部 */
.login-header {
  text-align: center;
  margin-bottom: 35px;
}

.icon-wrapper {
  margin-bottom: 25px;
  display: flex;
  justify-content: center;
}

.icon-bg {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6a00, #ff8533);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(255, 106, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.icon-bg::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

.login-icon {
  font-size: 2.5rem;
  color: white;
  z-index: 1;
}

.login-title {
  font-size: 2.2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ff6a00, #ff8533);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 12px;
  letter-spacing: -0.5px;
}

.login-subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
  font-weight: 400;
}

/* 登录表单 */
.login-form {
  margin-top: 35px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-form :deep(.el-form-item) {
  margin-bottom: 25px;
  width: 100%;
  max-width: 380px;
  display: flex;
  justify-content: center;
}

.input-wrapper {
  position: relative;
  width: 100%;
  max-width: 380px;
  margin: 0 auto;
}

.custom-input {
  width: 100%;
}

.custom-input :deep(.el-input__wrapper) {
  border-radius: 16px;
  border: 2px solid #e8e8e8;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 12px 16px;
  height: 56px;
  width: 100%;
}

.custom-input :deep(.el-input__wrapper:hover) {
  border-color: #ff8533;
  box-shadow: 0 6px 20px rgba(255, 133, 51, 0.15);
}

.custom-input :deep(.el-input__wrapper.is-focus) {
  border-color: #ff6a00;
  box-shadow: 0 0 0 3px rgba(255, 106, 0, 0.15);
}

.custom-input :deep(.el-input__inner) {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.custom-input :deep(.el-input__prefix) {
  color: #ff6a00;
}

/* 验证码容器 */
.captcha-container {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 380px;
  margin: 0 auto;
  gap: 12px;
}

.captcha-input {
  flex: 1;
}

.captcha-code {
  width: 120px;
  height: 56px;
  background: linear-gradient(135deg, #ff6a00, #ff8533);
  border: 2px solid #ff6a00;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  color: white;
  cursor: pointer;
  user-select: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(255, 106, 0, 0.3);
}

.captcha-code:hover {
  background: linear-gradient(135deg, #ff5500, #ff6a00);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 106, 0, 0.4);
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 380px;
  margin: 8px auto 0;
}

.remember-checkbox :deep(.el-checkbox__label) {
  color: #666;
  font-weight: 500;
}

.remember-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #ff6a00;
  border-color: #ff6a00;
}

.forgot-link {
  color: #ff6a00 !important;
  font-weight: 600;
  transition: all 0.3s ease;
}

.forgot-link:hover {
  color: #ff8533 !important;
}

/* 登录按钮 */
.login-button {
  width: 100%;
  max-width: 380px;
  height: 56px;
  border: none;
  border-radius: 28px;
  background: linear-gradient(135deg, #ff6a00, #ff8533);
  color: white;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(255, 106, 0, 0.3);
  margin: 10px auto 0;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 106, 0, 0.4);
  background: linear-gradient(135deg, #ff5500, #ff6a00);
}

.login-button:active:not(:disabled) {
  transform: translateY(-1px);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.button-icon {
  font-size: 1.2rem;
}

.button-text {
  font-weight: 700;
}

.button-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 28px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.login-button:hover .button-glow {
  opacity: 1;
}

/* 安全提示 */
.security-notice {
  margin-top: 15px;
  width: 100%;
  max-width: 380px;
  margin-left: auto;
  margin-right: auto;
}

.security-alert {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.05));
  border: 2px solid rgba(255, 193, 7, 0.3);
  border-radius: 16px;
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  backdrop-filter: blur(10px);
}

.security-icon {
  color: #ff8533;
  font-size: 1.2rem;
  margin-top: 2px;
  flex-shrink: 0;
}

.security-content {
  flex: 1;
}

.security-title {
  font-weight: 600;
  color: #ff6a00;
  margin-bottom: 4px;
  font-size: 14px;
}

.security-description {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .main-content {
    max-width: 100%;
    padding: 0 10px;
  }

  .card-content {
    padding: 30px 25px;
  }

  .login-title {
    font-size: 1.8rem;
  }

  .back-button {
    top: 5px;
  }

  .input-wrapper,
  .form-options,
  .login-button,
  .security-notice {
    max-width: 100%;
  }

  .captcha-container {
    flex-direction: column;
    gap: 15px;
  }

  .captcha-input {
    width: 100%;
  }

  .captcha-code {
    width: 100%;
  }
}
</style>
