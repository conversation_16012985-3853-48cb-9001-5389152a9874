import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 全局错误处理 - 忽略ResizeObserver错误
const originalError = console.error
console.error = function(...args) {
  if (args[0] && args[0].includes && args[0].includes('ResizeObserver loop completed with undelivered notifications')) {
    return
  }
  originalError.apply(console, args)
}

// 全局未捕获错误处理
window.addEventListener('error', (event) => {
  if (event.message && event.message.includes('ResizeObserver loop completed with undelivered notifications')) {
    event.preventDefault()
    return false
  }
})

const app = createApp(App)

// 注册路由
app.use(router)

// 注册Element Plus
app.use(ElementPlus)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.mount('#app')
