-- 商城前端模拟数据插入脚本
-- 数据库：pms
-- 用户：root
-- 密码：123456

SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

USE pms;

-- ========================================
-- 1. 清空现有数据（可选）
-- ========================================
-- DELETE FROM stock;
-- DELETE FROM product;
-- DELETE FROM category;

-- ========================================
-- 2. 插入商品分类数据
-- ========================================
INSERT INTO category (id, name) VALUES
(1001, '酒类'),
(1002, '饮料'),
(1003, '零食'),
(1004, '日用品'),
(1005, '电子产品')
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- ========================================
-- 3. 插入商品数据
-- ========================================
INSERT INTO product (id, name, category_id, price, description, image_url, status, create_time) VALUES
-- 酒类商品
(10001, '茅台（MOUTAI）飞天 酱香型白酒 53度 200ml 单瓶装', 1001, 299.00, '茅台（MOUTAI）飞天 酱香型白酒 53度 200ml 单瓶装，正宗茅台酒，酱香浓郁', '/images/maotai_200ml.jpg', 1, '2024-01-15 10:00:00'),
(10002, '五粮液 52度 500ml 浓香型白酒', 1001, 899.00, '五粮液经典装，五种粮食酿造，口感醇厚', '/images/wuliangye_500ml.jpg', 1, '2024-01-20 10:00:00'),
(10003, '可口可乐 330ml*24罐装', 1002, 48.00, '可口可乐经典装，畅爽一夏', '/images/coca_cola.jpg', 1, '2024-01-25 10:00:00'),
(10004, '剑南春 52度 500ml 浓香型白酒', 1001, 399.00, '剑南春经典装，传统工艺酿造', '/images/jiannanchun_500ml.jpg', 1, '2024-01-10 10:00:00'),
(10005, '华为 Mate 50 Pro 8GB+256GB', 1005, 6999.00, '华为旗舰手机，麒麟芯片，超强拍照', '/images/huawei_mate50.jpg', 1, '2024-02-01 10:00:00'),
(10006, 'iPhone 14 Pro 128GB 深空黑色', 1005, 7999.00, 'Apple iPhone 14 Pro，A16仿生芯片，专业摄像头系统', '/images/iphone14_pro.jpg', 1, '2024-02-05 10:00:00'),
(10007, '小米13 Ultra 12GB+256GB', 1005, 5999.00, '小米13 Ultra，徕卡专业光学镜头，骁龙8 Gen2', '/images/xiaomi13_ultra.jpg', 1, '2024-02-10 10:00:00'),
(10008, '农夫山泉天然水 550ml*24瓶', 1002, 45.00, '天然弱碱性水，口感甘甜，健康之选', '/images/water.jpg', 1, '2024-02-20 10:00:00'),
(10009, '三只松鼠坚果大礼包 1080g', 1003, 89.90, '精选优质坚果，营养健康，办公室零食首选', '/images/nuts_gift.jpg', 1, '2024-01-30 10:00:00'),
(10010, '良品铺子麻辣牛肉干 100g', 1003, 25.90, '精选优质牛肉，麻辣鲜香，嚼劲十足', '/images/beef_jerky.jpg', 1, '2024-02-25 10:00:00'),
(10011, '海飞丝去屑洗发水 400ml', 1004, 29.90, '有效去屑，温和清洁，让头发更健康', '/images/shampoo.jpg', 1, '2024-02-15 10:00:00'),
(10012, '舒肤佳香皂 125g*3块装', 1004, 19.90, '抗菌除菌，温和清洁，全家适用', '/images/soap.jpg', 1, '2024-03-01 10:00:00')
ON DUPLICATE KEY UPDATE 
    name = VALUES(name),
    category_id = VALUES(category_id),
    price = VALUES(price),
    description = VALUES(description),
    image_url = VALUES(image_url),
    status = VALUES(status),
    create_time = VALUES(create_time);

-- ========================================
-- 4. 插入库存数据
-- ========================================
INSERT INTO stock (product_id, quantity) VALUES
(10001, 50),   -- 茅台 200ml
(10002, 30),   -- 五粮液 500ml
(10003, 200),  -- 可口可乐
(10004, 25),   -- 剑南春 500ml
(10005, 15),   -- 华为 Mate 50 Pro
(10006, 20),   -- iPhone 14 Pro
(10007, 25),   -- 小米13 Ultra
(10008, 150),  -- 农夫山泉
(10009, 60),   -- 三只松鼠坚果
(10010, 45),   -- 良品铺子牛肉干
(10011, 80),   -- 海飞丝洗发水
(10012, 200)   -- 舒肤佳香皂
ON DUPLICATE KEY UPDATE
    quantity = VALUES(quantity);

-- ========================================
-- 5. 查询验证数据
-- ========================================
-- 查询所有分类
SELECT '=== 商品分类 ===' as info;
SELECT * FROM category ORDER BY id;

-- 查询所有商品及其分类信息
SELECT '=== 商品信息 ===' as info;
SELECT p.id, p.name, c.name as category_name, p.price, p.status, p.create_time 
FROM product p 
LEFT JOIN category c ON p.category_id = c.id 
ORDER BY p.id;

-- 查询商品库存信息
SELECT '=== 库存信息 ===' as info;
SELECT p.id, p.name, s.quantity,
       CASE WHEN s.quantity <= 10 THEN '库存不足' ELSE '库存充足' END as stock_status
FROM product p
LEFT JOIN stock s ON p.id = s.product_id
ORDER BY p.id;

-- 统计信息
SELECT '=== 统计信息 ===' as info;
SELECT 
    (SELECT COUNT(*) FROM category) as category_count,
    (SELECT COUNT(*) FROM product WHERE status = 1) as product_count,
    (SELECT COUNT(*) FROM stock) as stock_count;
