[Unit]
Description=Product Management System Backend
After=network.target mysql.service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/pms
ExecStart=/usr/bin/java -jar -Dspring.profiles.active=prod -Xms512m -Xmx1024m /opt/pms/product-management-system-1.0-SNAPSHOT.jar
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=JAVA_HOME=/usr/lib/jvm/java-17-openjdk
Environment=SPRING_PROFILES_ACTIVE=prod

# 安全设置
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
